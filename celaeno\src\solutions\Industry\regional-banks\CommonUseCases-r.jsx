import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faBullseye, faSync, faCompass, faComments } from '@fortawesome/free-solid-svg-icons'
import '../tech-saas/CommonUseCases.css'

function CommonUseCases_r() {
  const useCases = [
    {
      icon: faBullseye,
      title: "Branch-Level Goal Tracking",
      description: "Set and manage goals for service excellence, account growth, and member experience at the branch level."
    },
    {
      icon: faSync,
      title: "Structured Coaching Conversations",
      description: "Give managers tools to mentor and upskill frontline staff."
    },
    {
      icon: faCompass,
      title: "Compliance + Ethics Reviews",
      description: "Automate performance components linked to compliance, risk, and customer protection."
    },
    {
      icon: faComments,
      title: "Track Regional Team Performance Trends",
      description: "View performance insights across multiple branches and teams."
    }
  ]

  return (
    <section className="common-use-cases">
      <div className="common-use-cases-container">
        <h2 className="common-use-cases-title">Common Use Cases</h2>
        
        <div className="use-cases-grid-1">
          {useCases.map((useCase, index) => (
            <div key={index} className="use-case-item">
              <div className="use-case-icon">
                <FontAwesomeIcon icon={useCase.icon} />
              </div>
              <div className="use-case-content">
                <h3 className="use-case-title">{useCase.title}</h3>
                <p className="use-case-description-1">{useCase.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default CommonUseCases_r
