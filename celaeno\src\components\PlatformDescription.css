/* Platform Description Section */
.platform-description {
  background-color: #f1f1ec;
  padding: 5rem 0;
  position: relative;
}

.platform-description-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
}

.platform-description-title {
  font-size: 28px;
  font-weight: 700;
  color: #007474;
  line-height: 1.3;
  margin-bottom: 3rem;
  max-width: 900px;
  margin-left: 63px;
  margin-right: auto;
}

.platform-description-content {
  max-width: 1000px;
  margin: 0 auto;
}

.platform-description-text {
  font-size: 1.2rem;
  color: black;
  font-weight: 500;
  line-height: 1.7;
  margin-bottom: 2rem;
  text-align: left;
}

.platform-description-text:last-child {
  margin-bottom: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .platform-description {
    padding: 4rem 0;
  }

  .platform-description-title {
    font-size: 2rem;
    margin-bottom: 2.5rem;
  }

  .platform-description-text {
    font-size: 1.1rem;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .platform-description {
    padding: 3rem 0;
  }

  .platform-description-title {
    font-size: 1.8rem;
    margin-bottom: 2rem;
  }

  .platform-description-text {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }
}
