/* Filter Section */
.filter-section {
  background-color: #f1f1ec;
  padding: 6rem 0;
  min-height: 400px;
}

/* Header Section */
.filter-header {
  text-align: left;
  margin-bottom: 4rem;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding: 0 2rem;
}

.filter-main-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #007474;
  margin: 0 0 1rem 0;
  line-height: 1.2;
}

.filter-subtitle {
  font-size: 1.2rem;
  color: black;
  margin: 0;
  line-height: 1.5;
  font-weight: 700;
}

.filter-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filter-content {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 8rem;
  width: 100%;
  min-height: 200px;
}

.filter-left {
  flex: 0 0 auto;
  max-width: 220px;
}

.filter-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin-left: 18rem;
  max-width: 700px;
}

/* Filter Buttons */
.filter-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-btn {
  background-color: white;
  border: 2px solid #d1d5db;
  color: #6b7280;
  font-size: 1rem;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 0.5rem;
  white-space: nowrap;
  min-width: 200px;
  outline: none !important;
  box-shadow: none !important;
}

.filter-btn:focus {
  outline: none !important;
  box-shadow: none !important;
}

.filter-btn:focus-visible {
  outline: none !important;
  box-shadow: none !important;
}

.filter-btn:first-child {
  border-radius: 0.5rem;
}

.filter-btn:last-child {
  border-radius: 0.5rem;
}

.filter-btn.active {
  background-color: #007474;
  color: white;
  border-color: #007474;
}

/* Filter Points */
.filter-points {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.filter-point {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.filter-point-bullet {
  font-size: 1.2rem;
  color: #007474;
  font-weight: bold;
  line-height: 1;
  margin-top: 0.1rem;
  flex-shrink: 0;
}

.filter-point-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.filter-point-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #007474;
  margin: 0;
  line-height: 1.4;
  font-weight: 700;
}

.filter-point-description {
  font-size: 1rem;
  color: #4a5568;
  margin: 0;
  line-height: 1.5;
  margin-top: 0.1rem;
}

/* Filter Link */
.filter-link-container {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  flex: 1;
  margin-top: 40px;
}

.filter-link {
  color: #007474;
  font-size: 1rem;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.2s ease;
}

.filter-link:hover {
  color: #005555;
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
  .filter-section {
    padding: 4rem 0;
    min-height: 300px;
  }

  .filter-header {
    margin-bottom: 3rem;
    padding: 0 1rem;
  }

  .filter-main-title {
    font-size: 2rem;
  }

  .filter-subtitle {
    font-size: 1.1rem;
  }

  .filter-content {
    flex-direction: column;
    gap: 3rem;
    align-items: center;
  }

  .filter-left {
    width: 100%;
  }

  .filter-right {
    width: 100%;
  }

  .filter-points {
    gap: 1.25rem;
  }

  .filter-point-bullet {
    font-size: 1.1rem;
  }

  .filter-point-title {
    font-size: 0.95rem;
  }

  .filter-point-description {
    font-size: 0.9rem;
  }
  
  .filter-buttons {
    justify-content: center;
  }
  
  .filter-btn {
    font-size: 0.9rem;
    padding: 0.625rem 1.25rem;
  }
  
  .filter-link-container {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .filter-main-title {
    font-size: 1.75rem;
  }

  .filter-subtitle {
    font-size: 1rem;
  }

  .filter-container {
    padding: 0 1rem;
  }
  
  .filter-btn {
    font-size: 0.85rem;
    padding: 0.5rem 1rem;
  }
  
  .filter-point-bullet {
    font-size: 1.2rem;
  }

  .filter-point-title {
    font-size: 0.95rem;
  }

  .filter-point-description {
    font-size: 0.9rem;
  }

  .filter-link {
    font-size: 0.9rem;
  }
}
