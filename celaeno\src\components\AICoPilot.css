/* AI Co-Pilot Section */
.ai-copilot {
  background-color: white;
  padding: 5rem 0;
}

.ai-copilot-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* AI Co-Pilot Content */
.ai-copilot-content {
  display: flex;
  gap: 4rem;
  align-items: center;
  margin-bottom: 5rem;
}

.ai-copilot-text {
  flex: 1;
  max-width: 500px;
}

.ai-copilot-title {
  font-size: 37px;
  font-weight: 700;
  color: #007474;
  margin-bottom: 2rem;
  line-height: 1.2;
}

.ai-copilot-description {
  font-size: 1.2rem;
  color: #333;
  line-height: 1.6;
  margin: 0;
}

.ai-copilot-image {
  flex: 1;
  max-width: 500px;
}

.ai-copilot-img {
  width: 100%;
  height: auto;
  max-height: 300px;
  object-fit: contain;
  border-radius: 12px;
  margin-left: 140px;
}

/* Who It's For Section */
.who-its-for-section {
  margin-top: 5rem;
}

.who-its-for-header {
  display: flex;
  gap: 4rem;
  align-items: flex-start;
}

.who-its-for-title {
  font-size: 37px;
  font-weight: 700;
  color: #007474;
  margin-bottom: 0;
  line-height: 1.2;
  flex-shrink: 0;
  min-width: 200px;
}

.who-its-for-content {
  font-size: 1.2rem;
  color: #333;
  line-height: 1.6;
  margin: 0;
  flex: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ai-copilot {
    padding: 4rem 0;
  }

  .ai-copilot-content {
    flex-direction: column;
    gap: 3rem;
    margin-bottom: 4rem;
    text-align: center;
  }

  .ai-copilot-text {
    max-width: 100%;
  }

  .ai-copilot-title {
    font-size: 2rem;
    margin-bottom: 1.5rem;
  }

  .ai-copilot-description {
    font-size: 1.1rem;
  }

  .ai-copilot-image {
    max-width: 100%;
  }

  .ai-copilot-img {
    max-height: 350px;
    margin-left: 0;
  }

  .who-its-for-header {
    flex-direction: column;
    gap: 2rem;
  }

  .who-its-for-title {
    font-size: 2rem;
    margin-bottom: 0;
    text-align: center;
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .ai-copilot {
    padding: 3rem 0;
  }

  .ai-copilot-content {
    gap: 2.5rem;
    margin-bottom: 3rem;
  }

  .ai-copilot-title {
    font-size: 1.8rem;
  }

  .ai-copilot-description {
    font-size: 1rem;
  }

  .ai-copilot-img {
    max-height: 280px;
    margin-left: 0;
  }

  .who-its-for-title {
    font-size: 1.8rem;
    margin-bottom: 0;
  }
}
