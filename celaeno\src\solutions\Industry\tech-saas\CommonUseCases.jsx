import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faBullseye, faSync, faCompass, faComments } from '@fortawesome/free-solid-svg-icons'
import './CommonUseCases.css'

function CommonUseCases() {
  const useCases = [
    {
      icon: faBullseye,
      title: "OKR Alignment Across Functions",
      description: "Ensure product, marketing, and engineering are focused on what drives growth."
    },
    {
      icon: faSync,
      title: "Continuous Feedback in Agile Environments",
      description: "Enable devs, PMs, and customer success teams to exchange rapid feedback within sprints."
    },
    {
      icon: faCompass,
      title: "Role-Based Goal Tracking",
      description: "Define engineering, product, or GTM KPIs that actually reflect team velocity and outcomes."
    },
    {
      icon: faComments,
      title: "1:1 Syncs with Technical Managers",
      description: "Build structured check-ins across hybrid or distributed teams."
    }
  ]

  return (
    <section className="common-use-cases">
      <div className="common-use-cases-container">
        <h2 className="common-use-cases-title">Common Use Cases</h2>
        
        <div className="use-cases-grid-1">
          {useCases.map((useCase, index) => (
            <div key={index} className="use-case-item">
              <div className="use-case-icon">
                <FontAwesomeIcon icon={useCase.icon} />
              </div>
              <div className="use-case-content">
                <h3 className="use-case-title">{useCase.title}</h3>
                <p className="use-case-description-1">{useCase.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default CommonUseCases
