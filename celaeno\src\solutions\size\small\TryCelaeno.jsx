import { useNavigate } from 'react-router-dom'
import './TryCelaeno.css'

function TryCelaeno() {
  const navigate = useNavigate()

  const handleStartTrialClick = () => {
    navigate('/signup')
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }, 100)
  }

  const handleTalkToSpecialistClick = () => {
    navigate('/talk-to-sales')
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }, 100)
  }

  return (
    <section className="try-celaeno">
      <div className="try-celaeno-container">
        <div className="try-celaeno-content">
          <h2 className="try-celaeno-title">Try Celaeno Performance</h2>
          <p className="try-celaeno-subtitle">Free – No Credit Card Needed</p>
          
          <div className="try-celaeno-buttons">
            <button 
              className="start-trial-btn"
              onClick={handleStartTrialClick}
            >
              Start Free Trial
            </button>
            <button 
              className="talk-specialist-btn"
              onClick={handleTalkToSpecialistClick}
            >
              Talk to a Product Specialist
            </button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default TryCelaeno
