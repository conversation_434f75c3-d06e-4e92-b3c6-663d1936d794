import './TechSaasOverview.css'

function TechSaasOverview() {
  return (
    <section className="tech-saas-overview">
      <div className="tech-saas-overview-container">
        <div className="tech-saas-overview-content">
          <h2 className="tech-saas-overview-title">
            Built for Startups, Scaleups & Global SaaS Teams
          </h2>
          <p className="tech-saas-overview-description">
            Whether you're a seed-stage startup or scaling across multiple time zones, Celaeno adapts to your speed. 
            Set OKRs, run real-time feedback loops, and manage team performance in the tools your tech teams already use.
          </p>
        </div>
      </div>
    </section>
  )
}

export default TechSaasOverview
