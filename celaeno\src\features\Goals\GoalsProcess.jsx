import '../one-on-one/OneOnOneProcess.css'


function GoalsProcess() {
  const processSteps = [
    {
      title: "Set Your Goal",
      description: "Use a guided template or create custom goals with clear metrics and timelines."
    },
    {
      title: "Align with the Team",
      description: "Link your goal to a higher-level objective and make it visible to stakeholders."
    },
    {
      title: "Update & Celebrate",
      description: "Track milestones and update progress. Celebrate completion with feedback or badges."
    }
  ]

  return (
    <section className="one-on-one-process">
      <div className="one-on-one-process-container">
        <h2 className="one-on-one-process-title">How It Works (Visual Flow)</h2>
        {/* SVG with curved line and positioned dots */}
        <div className="process-curve-container">
          <svg viewBox="0 0 1000 300" className="process-curve-svg">
            {/* Curved line */}
            <path
              d="M 0 200 Q 300 80 500 150 Q 700 220 950 120 Q 1000 100 1100 110"
              stroke="#333"
              strokeWidth="2"
              fill="none"
            />

            {/* Dots positioned on the curve */}
            <circle cx="150" cy="150" r="12" fill="#007474" />
            <circle cx="500" cy="150" r="12" fill="#007474" />
            <circle cx="900" cy="140" r="12" fill="#007474" />
          </svg>
        </div>

        {/* Text content positioned below dots */}
        <div className="process-steps">
          {processSteps.map((step, index) => (
            <div key={index} className="process-step">
              <h3 className="process-step-title">{step.title}</h3>
              <p className="process-step-description">{step.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Benefits Cards - Positioned to span between process and footer */}
      <div className="benefits-overlay">
        <div className="benefits-overlay-container">
          <div>
            <h2 className="benefits-overlay-title">For All Levels</h2>
          </div>
          <div className="benefits-cards">
            <div className="benefit-card">
              <h3 className="benefit-title">Employees:</h3>
              <p className="benefit-description">Know what matters and how to move the needle</p>
            </div>
            <div className="benefit-card">
              <h3 className="benefit-title">Managers:</h3>
              <p className="benefit-description">Align work to business priorities</p>
            </div>
            <div className="benefit-card">
              <h3 className="benefit-title">HR/Leadership:</h3>
              <p className="benefit-description">Get visibility into progress and identify gaps</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default GoalsProcess
