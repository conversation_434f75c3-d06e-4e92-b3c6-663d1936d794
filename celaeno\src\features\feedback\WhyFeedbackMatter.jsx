import '../one-on-one/WhyOneOnOnesMatter.css'

function WhyFeedbackMatter() {
  return (
    <section className="why-one-on-ones-matter">
      <div className="why-one-on-ones-container">
        <div className="why-one-on-ones-content">
          <h2 className="why-one-on-ones-title">Why Feedback Matters</h2>
          <p className="why-one-on-ones-description">
           The best-performing teams don't wait until reviews to give feedback. With Ce<PERSON>eno's real-time feedback module, recognition and coaching become part of your everyday culture — boosting morale, reducing blind spots, and enabling continuous development.
          </p>
          <p className="why-one-on-ones-subtitle">
            Whether it's a quick “thank you” or structured coaching, everything lives in one place.
          </p>
        </div>
      </div>
    </section>
  )
}

export default WhyFeedbackMatter
