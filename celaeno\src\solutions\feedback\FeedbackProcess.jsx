import './FeedbackProcess.css'

function FeedbackProcess() {
  const processSteps = [
    {
      title: "Start the Conversation",
      description: "Click “Give Feedback” from any teammate’s profile or directly from a meeting or goal."
    },
    {
      title: "Choose Your Format",
      description: "Quick note, structured form, or skill-based tagging? Pick your style."
    },
    {
      title: "Send & Track",
      description: "Feedback gets logged automatically to the employee’s growth timeline."
    }
  ]

  return (
    <section className="one-on-one-process">
      <div className="one-on-one-process-container">
        <h2 className="one-on-one-process-title">How It Works (Flow)</h2>
        {/* SVG with curved line and positioned dots */}
        <div className="process-curve-container">
          <svg viewBox="0 0 1000 300" className="process-curve-svg">
            {/* Curved line */}
            <path
              d="M 0 200 Q 300 80 500 150 Q 700 220 950 120 Q 1000 100 1100 110"
              stroke="#333"
              strokeWidth="2"
              fill="none"
            />

            {/* Dots positioned on the curve */}
            <circle cx="150" cy="150" r="12" fill="#007474" />
            <circle cx="500" cy="150" r="12" fill="#007474" />
            <circle cx="900" cy="140" r="12" fill="#007474" />
          </svg>
        </div>

        {/* Text content positioned below dots */}
        <div className="process-steps">
          {processSteps.map((step, index) => (
            <div key={index} className="process-step">
              <h3 className="process-step-title">{step.title}</h3>
              <p className="process-step-description">{step.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Benefits Cards - Positioned to span between process and footer */}
      <div className="benefits-overlay">
        <div className="benefits-overlay-container">
          <div>
            <h2 className="benefits-overlay-title">Designed for Everyone</h2>
          </div>
          <div className="benefits-cards">
            <div className="benefit-card">
              <h3 className="benefit-title">Employees:</h3>
              <p className="benefit-description">Get recognized and coached in real time</p>
            </div>
            <div className="benefit-card">
              <h3 className="benefit-title">Managers:</h3>
              <p className="benefit-description">Give better, more consistent input with less friction</p>
            </div>
            <div className="benefit-card">
              <h3 className="benefit-title">HR Teams:</h3>
              <p className="benefit-description">Roll out organization-wide feedback initiatives with ease</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default FeedbackProcess
