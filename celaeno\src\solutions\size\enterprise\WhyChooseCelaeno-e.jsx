import '../small/WhyChooseCelaeno.css'

function WhyChooseCelaeno_m() {
  return (
    <section className="why-choose-celaeno">
      <div className='gradient-left'>
        
      </div>
      <div className="why-choose-container">
        <div className="why-choose-content">
          <h2 className="why-choose-title">Why Enterprises Choose Celaeno</h2>
          
        
          
          <div className="why-choose-features">
            <div className="feature-item">
              <h3>Role- and location-based permission controls</h3>
            </div>
            <div className="feature-item">
              <h3>Custom workflows for every business unit</h3>
            </div>
            <div className="feature-item">
              <h3>SSO, SCIM, SOC2, and GDPR-compliant infrastructure</h3>
            </div>
            <div className="feature-item">
              <h3>Real-time analytics, benchmarks, and audit logs</h3>
            </div>
          </div>
          
          <p className="why-choose-conclusion">
            Whether you’re in financial services, healthcare, manufacturing, or global tech — Celaeno adapts to your policies, tools, and scale.
          </p>
        </div>
      </div>
    </section>
  )
}

export default WhyChooseCelaeno_m
