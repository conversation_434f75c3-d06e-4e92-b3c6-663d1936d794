import '../small/SampleUseCases.css'

function UseCases_m() {
  const useCases = [
    {
      title: "Global Financial Institutions",
      description: "Align risk, compliance, and business performance."
    },
    {
      title: "Enterprise Life Sciences & Pharma",
      description: "Track growth, learning, and performance alongside SOPs and audits."
    },
    {
      title: "Manufacturing Enterprises",
      description: "Enable hourly employee reviews and factory-floor feedback via kiosk mode."
    }
  ]

  return (
    <section className="sample-use-cases">
      <div className="sample-use-cases-container">
        <h2 className="sample-use-cases-title">Common Use Cases</h2>
        
        <div className="use-cases-grid">
          {useCases.map((useCase, index) => (
            <div key={index} className="use-case-card">
              <div className="use-case-content">
                <h3 className="use-case-title">{useCase.title}</h3>
                <p className="use-case-description">{useCase.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default UseCases_m
