/* Pricing Header Section */
.pricing-header-section {
  padding: 6rem 0 8rem 0;
  background: linear-gradient(180deg, white 50%, #4fa6a4 40%);
  text-align: center;
  position: relative;
}

.pricing-header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
}

.pricing-header-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #007474;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.pricing-header-description {
  font-weight: 700;
  font-size: 1.1rem;
  color: #333;
  max-width: 600px;
  margin: 0 auto 2.5rem auto;
  line-height: 1.6;
}

.billing-toggle {
  display: flex;
  background: white;
  border-radius: 50px;
  padding: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  width: fit-content;
  margin: 0 auto;
}

.toggle-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 50px;
  background: transparent;
  color: #666;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-btn.active {
  background: #007474;
  color: white;
}

/* Pricing Cards */
.pricing-cards {
  display: flex;
  gap: 2rem;
  justify-content: center;
  align-items: flex-start;
  margin-top: 4rem;
}

.pricing-card {
  border-radius: 1rem;
  padding: 2.5rem 2rem;
  text-align: left;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  position: relative;
  width: 350px;
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* Starter Card - Light background with teal text */
.pricing-card.starter {
  background: #f1f1ec;
  color: #007474;
  min-height: 600px;
}

/* Growth Card - Featured card with medium height */
.pricing-card.growth {
  min-height: 650px;
}

/* Enterprise Card - White background with lighter teal text */
.pricing-card.enterprise {
  background: white;
  color: #4fa6a4;
  min-height: 700px;
}

.pricing-card:hover {
  transform: translateY(-5px);
}

.pricing-card.featured {
  background: #007474;
  color: white;
  transform: scale(1.05);
  z-index: 2;
}

.pricing-card.featured:hover {
  transform: scale(1.05) translateY(-5px);
}

.card-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: inherit;
}

.pricing-card.starter .card-title {
  color: #007474;
}

.pricing-card.enterprise .card-title {
  color: #4fa6a4;
}

.pricing-card.featured .card-title {
  color: white;
}

.card-subtitle {
  font-weight: 700;
  font-size: 1rem;
  color: #666;
  margin-bottom: 2rem;
}

.pricing-card.starter .card-subtitle {
  color: #007474;
}

.pricing-card.enterprise .card-subtitle {
  color: #4fa6a4;
}

.pricing-card.featured .card-subtitle {
  color: rgba(255, 255, 255, 0.8);
}

/* Feature List */
.feature-list {
  font-weight: 700;
  list-style: none;
  padding: 0;
  margin: 2rem 0 0 0;
  text-align: left;
}

.feature-item-p {
  color: inherit;
  font-size: 1rem;
  margin-bottom: 0.75rem;
  line-height: 1.4;
}

.pricing-card.starter .feature-item-p {
  color: #007474;
}

.pricing-card.featured .feature-item-p {
  color: white;
}

/* Feature Intro */
.feature-intro {
  color: inherit;
  font-size: 1rem;
  font-weight: 600;
  margin: 1.5rem 0 1rem 0;
  text-align: left;
}

.pricing-card.featured .feature-intro {
  font-weight: 700;
  color: rgba(255, 255, 255, 0.9);
}

.pricing-card.enterprise .feature-item-p {
  color: #4fa6a4;
}

.pricing-card.enterprise .feature-intro {
  color: #4fa6a4;
  font-weight: 700;
}

.price {
  margin-bottom: 2.5rem;
}

.price-amount {
  font-size: 2.5rem;
  font-weight: 700;
  color: inherit;
}

.pricing-card.starter .price-amount {
  color: #007474;
}

.pricing-card.enterprise .price-amount {
  color: #4fa6a4;
}

.price-period {
  font-weight: 700;
  font-size: 1.5rem;
  color: #007474;
  margin-left: 0.25rem;
}

.pricing-card.featured .price-period {
  color: rgba(255, 255, 255, 0.8);
}

.get-started-btn {
  background: #4fa6a4;
  color: white;
  border: none;
  padding: 0.875rem 2rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
  width: 100%;
  text-align: center;
}

.get-started-btn:hover {
  background: #3d8a88;
}

.pricing-card.featured .get-started-btn {
  background: white;
  color: #007474;
}

.pricing-card.featured .get-started-btn:hover {
  background: #f8f9fa;
}

.curve-divider {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 675px;
  overflow: hidden;
  z-index: 1;
}

.curve-divider svg {
  width: 100%;
  height: 100%;
  display: block;
}


/* Downward Curve */
.pricing-header-section {
  padding: 6rem 0 8rem 0;
  background: white;
  text-align: center;
  position: relative;
  overflow: hidden; /* ensures ::after or SVG doesn't spill */
}


/* Responsive Design */
@media (max-width: 968px) {
  .pricing-header-section {
    padding: 4rem 0 6rem 0;
  }

  .pricing-cards {
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
  }

  .pricing-card {
    width: 100%;
    max-width: 350px;
  }

  .pricing-card.featured {
    transform: none;
  }

  .pricing-card.featured:hover {
    transform: translateY(-5px);
  }
}

@media (max-width: 768px) {
  .pricing-header-section {
    padding: 3rem 0 5rem 0;
  }

  .pricing-header-container {
    padding: 0 1rem;
  }

  .pricing-header-title {
    font-size: 2rem;
  }

  .pricing-header-description {
    font-size: 1rem;
  }

  .pricing-card {
    padding: 2rem 1.5rem;
  }
}

@media (max-width: 480px) {
  .pricing-header-title {
    font-size: 1.8rem;
  }

  .toggle-btn {
    padding: 0.625rem 1.25rem;
    font-size: 0.9rem;
  }

  .price-amount {
    font-size: 2rem;
  }
}
