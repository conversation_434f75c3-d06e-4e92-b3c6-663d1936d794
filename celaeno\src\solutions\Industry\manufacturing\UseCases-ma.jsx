import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faBullseye, faSync, faCompass, faComments } from '@fortawesome/free-solid-svg-icons'
import '../tech-saas/CommonUseCases.css'

function UseCases_ma() {
  const useCases = [
    {
      icon: faBullseye,
      title: "Align Teams on Safety, Quality & Throughput KPIs",
      description: "Set shift-specific goals and metrics at the line, team, or site level."
    },
    {
      icon: faSync,
      title: "Track Daily Standups & Supervisor 1:1s",
      description: "Standardize feedback and coaching conversations during line walks."
    },
    {
      icon: faCompass,
      title: "Monitor Role-Specific Goals for Operators, Leads & Engineers",
      description: "Ensure clear expectations across departments — from production to maintenance."
    },
    {
      icon: faComments,
      title: "Upskill Frontline Workers Through Structured Development Plans",
      description: "Map career pathways and compliance training progress."
    }
  ]

  return (
    <section className="common-use-cases">
      <div className="common-use-cases-container">
        <h2 className="common-use-cases-title">Common Use Cases</h2>
        
        <div className="use-cases-grid-1">
          {useCases.map((useCase, index) => (
            <div key={index} className="use-case-item">
              <div className="use-case-icon">
                <FontAwesomeIcon icon={useCase.icon} />
              </div>
              <div className="use-case-content">
                <h3 className="use-case-title">{useCase.title}</h3>
                <p className="use-case-description-1">{useCase.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default UseCases_ma
