import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faBullseye, faSync, faCompass, faComments } from '@fortawesome/free-solid-svg-icons'
import '../tech-saas/CommonUseCases.css'

function CommonUseCases_c() {
  const useCases = [
    {
      icon: faBullseye,
      title: "Align Branch & Back-Office Goals",
      description: "Drive performance across departments — from member service to loan processing and compliance."
    },
    {
      icon: faSync,
      title: "Run Quarterly or Biannual Reviews",
      description: "Use lightweight templates tailored for retail banking and support roles."
    },
    {
      icon: faCompass,
      title: "Track Coaching Conversations at the Branch Level",
      description: "Empower managers to coach tellers, service reps, and back-office staff."
    },
    {
      icon: faComments,
      title: "Support Regulatory & Operational Standards",
      description: "Keep teams on track with compliance-based evaluations and documentation logs."
    }
  ]

  return (
    <section className="common-use-cases">
      <div className="common-use-cases-container">
        <h2 className="common-use-cases-title">Common Use Cases</h2>
        
        <div className="use-cases-grid-1">
          {useCases.map((useCase, index) => (
            <div key={index} className="use-case-item">
              <div className="use-case-icon">
                <FontAwesomeIcon icon={useCase.icon} />
              </div>
              <div className="use-case-content">
                <h3 className="use-case-title">{useCase.title}</h3>
                <p className="use-case-description-1">{useCase.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default CommonUseCases_c
