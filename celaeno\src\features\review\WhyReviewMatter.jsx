import '../one-on-one/WhyOneOnOnesMatter.css'

function WhyReviewMatter() {
  return (
    <section className="why-one-on-ones-matter">
      <div className="why-one-on-ones-container">
        <div className="why-one-on-ones-content">
          <h2 className="why-one-on-ones-title">Why <PERSON><PERSON> for Reviews?</h2>
          <p className="why-one-on-ones-description">
            Traditional reviews are outdated, biased, and disconnected from daily work. <PERSON><PERSON><PERSON> reinvents the process with flexible, evidence-based, and human-centered review cycles.


          </p>
          <p className="why-one-on-ones-subtitle">
            Whether it's annual, quarterly, or on-demand our tools ensure feedback is grounded in goals, 1:1s, feedback trends, and peer insights.
          </p>
        </div>
      </div>
    </section>
  )
}

export default WhyReviewMatter
