/* One-on-One Features Section */
.one-on-one-features {
  margin-top: -55px;
  padding: 6rem 0;
  background-color: #f1f1ec;
}

.one-on-one-features-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.one-on-one-features-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #007474;
  margin-bottom: 3rem;
  line-height: 1.2;
}

.one-on-one-features-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 2rem;
}

.one-on-one-feature-card {
  background: white;
  padding: 2.5rem 2rem;
  border-radius: 1rem;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #e9ecef;
}

.one-on-one-feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.one-on-one-feature-icon {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem auto;
  font-size: 2rem;
  color: #ff6b35;
}

.one-on-one-feature-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: #007474;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.one-on-one-feature-description {
  font-weight: 700;
  font-size: 1rem;
  line-height: 1.6;
  color: #555;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 968px) {
  .one-on-one-features-title {
    font-size: 2.2rem;
    margin-bottom: 2.5rem;
  }

  .one-on-one-features-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  .one-on-one-feature-card {
    padding: 2rem 1.5rem;
  }

  .one-on-one-feature-icon {
    width: 70px;
    height: 70px;
    font-size: 1.8rem;
  }

  .one-on-one-feature-title {
    font-size: 1.2rem;
  }

  .one-on-one-feature-description {
    font-size: 0.95rem;
  }
}

@media (max-width: 768px) {
  .one-on-one-features {
    padding: 4rem 0;
  }

  .one-on-one-features-title {
    font-size: 2rem;
    margin-bottom: 2rem;
  }

  .one-on-one-features-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .one-on-one-feature-card {
    padding: 2rem 1.5rem;
  }

  .one-on-one-feature-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }

  .one-on-one-feature-title {
    font-size: 1.1rem;
    margin-bottom: 0.8rem;
  }

  .one-on-one-feature-description {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .one-on-one-features {
    padding: 3rem 0;
  }

  .one-on-one-features-title {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
  }

  .one-on-one-features-container {
    padding: 0 1rem;
  }

  .one-on-one-features-grid {
    gap: 1.5rem;
  }

  .one-on-one-feature-card {
    padding: 1.5rem 1rem;
  }

  .one-on-one-feature-icon {
    width: 50px;
    height: 50px;
    font-size: 1.3rem;
  }

  .one-on-one-feature-title {
    font-size: 1rem;
  }

  .one-on-one-feature-description {
    font-size: 0.85rem;
  }
}
