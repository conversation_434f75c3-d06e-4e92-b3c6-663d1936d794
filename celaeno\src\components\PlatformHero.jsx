import './PlatformHero.css'
import { useNavigate } from 'react-router-dom'

function PlatformHero() {
  const navigate = useNavigate()

  const handleGetStartedClick = () => {
    navigate('/pricing')
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }, 100)
  }

  return (
    <section className="platform-hero">
      <div className="platform-hero-container">
        <div className="platform-hero-content">
          <h1 className="platform-hero-title">
            One Platform-Endless Potential<br />
            Built for Modern Teams
          </h1>
          <p className="platform-hero-description">
            Everything your team needs to grow, stay aligned, and perform at their<br />
            best — in one seamless performance management system.
          </p>
          <button className="platform-hero-btn" onClick={handleGetStartedClick}>Get Started</button>
        </div>
      </div>
      
      {/* Background Decorative Circles */}
      <div className="platform-hero-circle platform-hero-circle-left"></div>
      <div className="platform-hero-circle platform-hero-circle-right"></div>
    </section>
  )
}

export default PlatformHero
