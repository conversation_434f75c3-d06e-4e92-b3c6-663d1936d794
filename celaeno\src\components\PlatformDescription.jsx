import './PlatformDescription.css'

function PlatformDescription() {
  return (
    <section className="platform-description">
      <div className="platform-description-container">
        <h2 className="platform-description-title">
          Celaeno is not just another HR tool. It's a performance powerhouse.
        </h2>
        
        <div className="platform-description-content">
          <p className="platform-description-text">
            Built to simplify the employee lifecycle and supercharge team output, our modular platform helps HR leaders, 
            managers, and employees stay connected, aligned, and continuously improving — no matter your team size or 
            structure.
          </p>
          
          <p className="platform-description-text">
            From day-to-day check-ins to quarterly reviews and long-term personal development, Celaeno ties it all together 
            with intuitive design, automation, and smart analytics.
          </p>
        </div>
      </div>
    </section>
  )
}

export default PlatformDescription
