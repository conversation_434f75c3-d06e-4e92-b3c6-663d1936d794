import { useState } from 'react'
import './FAQ.css'

function FAQ() {
  const [openIndex, setOpenIndex] = useState(null)

  const faqs = [
    {
      id: 1,
      question: "DO YOU DO WEB DESIGN OR WEB DEVELOPMENT?",
      answer: "We provide comprehensive web solutions including both design and development services. Our team handles everything from initial concept and UI/UX design to full-stack development and deployment."
    },
    {
      id: 2,
      question: "DO YOU DO WEB DESIGN OR WEB DEVELOPMENT?",
      answer: "We provide comprehensive web solutions including both design and development services. Our team handles everything from initial concept and UI/UX design to full-stack development and deployment."
    },
    {
      id: 3,
      question: "DO YOU DO WEB DESIGN OR WEB DEVELOPMENT?",
      answer: "We provide comprehensive web solutions including both design and development services. Our team handles everything from initial concept and UI/UX design to full-stack development and deployment."
    },
    {
      id: 4,
      question: "DO YOU DO WEB DESIGN OR WEB DEVELOPMENT?",
      answer: "We provide comprehensive web solutions including both design and development services. Our team handles everything from initial concept and UI/UX design to full-stack development and deployment."
    }
  ]

  const toggleFAQ = (index) => {
    setOpenIndex(openIndex === index ? null : index)
  }

  return (
    <section className="faq-section">
      <div className="faq-container">
        <h2 className="faq-title">Frequently Asked Questions</h2>
        
        <div className="faq-list">
          {faqs.map((faq, index) => (
            <div key={faq.id} className="faq-item">
              <button 
                className="faq-question"
                onClick={() => toggleFAQ(index)}
                aria-expanded={openIndex === index}
              >
                <span className="faq-question-text">{faq.question}</span>
                <span className={`faq-icon ${openIndex === index ? 'open' : ''}`}>
                  +
                </span>
              </button>
              
              {openIndex === index && (
                <div className="faq-answer">
                  <p>{faq.answer}</p>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default FAQ
