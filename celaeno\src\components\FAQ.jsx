import { useState } from 'react'
import './FAQ.css'

function FAQ() {
  const [openIndex, setOpenIndex] = useState(null)

  const faqs = [
    {
      id: 1,
      question: "Is there a free trial?",
      answer: "Yes, we offer a 14-day free trial — no credit card required."
    },
    {
      id: 2,
      question: "Can I change my plan later?",
      answer: "Yes, upgrade/downgrade anytime via the dashboard."
    },
    {
      id: 3,
      question: "Do you offer annual billing discounts?",
      answer: "Yes, save up to 20% with yearly billing."
    },
    {
      id: 4,
      question: "What if I need help?",
      answer: "We offer live chat, email support, and detailed documentation, plus onboarding help for new teams."
    },
    {
      id: 5,
      question: "How does the AI assistant help?",
      answer: "From summarizing 1:1s to suggesting action items and goal alignment, the AI Assistant lightens your workload."
    }
  ]

  const toggleFAQ = (index) => {
    setOpenIndex(openIndex === index ? null : index)
  }

  return (
    <section className="faq-section">
      <div className="faq-container">
        <h2 className="faq-title">Frequently Asked Questions</h2>
        
        <div className="faq-list">
          {faqs.map((faq, index) => (
            <div key={faq.id} className="faq-item">
              <button 
                className="faq-question"
                onClick={() => toggleFAQ(index)}
                aria-expanded={openIndex === index}
              >
                <span className="faq-question-text">{faq.question}</span>
                <span className={`faq-icon ${openIndex === index ? 'open' : ''}`}>
                  +
                </span>
              </button>
              
              {openIndex === index && (
                <div className="faq-answer">
                  <p>{faq.answer}</p>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default FAQ
