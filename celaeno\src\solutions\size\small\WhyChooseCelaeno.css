.why-choose-celaeno {
  padding: 4rem 0;
  background-color: white;
  position: relative;
}

.why-choose-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.why-choose-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.why-choose-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 2rem;
  line-height: 1.2;
}

.why-choose-intro {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 2.5rem;
  line-height: 1.6;
}

.why-choose-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2.5rem;
}

.feature-item {
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-radius: 12px;
  border-left: 4px solid #ff8c2b;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.feature-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.feature-item h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1.4;
}

.why-choose-conclusion {
  font-size: 1.2rem;
  color: #333;
  font-weight: 500;
  line-height: 1.6;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .why-choose-celaeno {
    padding: 3rem 0;
  }
  
  .why-choose-container {
    padding: 0 1rem;
  }
  
  .why-choose-title {
    font-size: 2rem;
    margin-bottom: 1.5rem;
  }
  
  .why-choose-intro {
    font-size: 1.1rem;
    margin-bottom: 2rem;
  }
  
  .why-choose-features {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 2rem;
  }
  
  .feature-item {
    padding: 1.2rem;
  }
  
  .why-choose-conclusion {
    font-size: 1.1rem;
  }
}
