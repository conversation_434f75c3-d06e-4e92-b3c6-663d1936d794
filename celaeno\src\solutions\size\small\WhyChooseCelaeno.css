.why-choose-celaeno {
  margin-top: -73px;
  padding: 4rem 0;
  background-color: white;
  position: relative;
}

.why-choose-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.why-choose-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.why-choose-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #007474;
  margin-bottom: 2rem;
  line-height: 1.2;
}

.why-choose-intro {
  font-weight: 600;
  font-size: 1.2rem;
  color: black;
  margin-bottom: 2.5rem;
  line-height: 1.6;
}

.why-choose-features {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  margin-bottom: 2.5rem;
}

.gradient-left {
  position: absolute;
  width: 600px;
  height: 600px;
  background: radial-gradient(circle, rgba(255, 171, 104, 0.5), transparent 70%);
  left: -25%;
  top: -12%;
  z-index: 2;
}
  
.feature-item {
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-radius: 12px;
  border-left: 4px solid #ff8c2b;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.feature-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.feature-item h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1.4;
}

.why-choose-conclusion {
  font-size: 1.2rem;
  color: #333;
  font-weight: 500;
  line-height: 1.6;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .why-choose-features {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.2rem;
  }
}

@media (max-width: 768px) {
  .why-choose-celaeno {
    padding: 3rem 0;
  }

  .why-choose-container {
    padding: 0 1rem;
  }

  .why-choose-title {
    font-size: 2rem;
    margin-bottom: 1.5rem;
  }

  .why-choose-intro {
    font-size: 1.1rem;
    margin-bottom: 2rem;
  }

  .why-choose-features {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .feature-item {
    padding: 1.2rem;
  }

  .why-choose-conclusion {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .why-choose-celaeno {
    padding: 2rem 0;
  }

  .why-choose-container {
    padding: 0 0.5rem;
  }

  .why-choose-title {
    font-size: 1.8rem;
    margin-bottom: 1.2rem;
  }

  .why-choose-intro {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }

  .feature-item {
    padding: 1rem;
  }

  .feature-item h3 {
    font-size: 1rem;
  }

  .why-choose-conclusion {
    font-size: 1rem;
  }
}
