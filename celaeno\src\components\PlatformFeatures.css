/* Platform Features Section */
.platform-features {
  background-color: white;
  padding: 5rem 0;
}

.platform-features-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.platform-features-grid {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  max-width: 900px;
  margin: 0 auto;
}

.platform-feature-card {
  display: flex;
  gap: 2rem;
  padding: 1rem 0;
  align-items: flex-start;
}

.platform-feature-image {
  flex-shrink: 0;
  width: 280px;
  height: 180px;
}

.platform-feature-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f0f9f9 0%, #e6f4f4 100%);
  border-radius: 8px;
  border: 2px solid #e0f0f0;
}

.platform-feature-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-top: 0.5rem;
  margin-left: 150px;
}

.platform-feature-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #007474;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.platform-feature-description {
  font-size: 1.1rem;
  color: #333;
  line-height: 1.6;
  margin-bottom: 2rem;
  flex-grow: 1;
}

.platform-feature-explore {
  background: none;
  border: none;
  color: #4fa6a4;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0;
  align-self: flex-end;
  margin-top: auto;
  transition: all 0.3s ease;
}

.gradient-circle-left {
  position: absolute;
  width: 600px;
  height: 600px;
  background: radial-gradient(circle, rgba(255, 171, 104, 0.5), transparent 70%);
  left: -25%;
}

.gradient-circle-right {
  position: absolute;
  width: 600px;
  height: 600px;
  background: radial-gradient(circle, rgba(255, 171, 104, 0.5), transparent 70%);
  right: -25%;
  top: 230%;
}

.explore-arrow {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.platform-feature-explore:hover .explore-arrow {
  transform: translateX(3px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .platform-features {
    padding: 4rem 0;
  }

  .platform-features-grid {
    gap: 1.5rem;
  }

  .platform-feature-card {
    flex-direction: column;
    text-align: center;
    padding: 1rem 0;
  }

  .platform-feature-image {
    width: 100%;
    height: 150px;
    align-self: center;
  }

  .platform-feature-content {
    text-align: center;
    padding-top: 1rem;
  }

  .platform-feature-title {
    font-size: 1.3rem;
  }

  .platform-feature-description {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }

  .platform-feature-explore {
    align-self: center;
  }
}

@media (max-width: 480px) {
  .platform-features {
    padding: 3rem 0;
  }

  .platform-features-grid {
    gap: 1.25rem;
  }

  .platform-feature-card {
    padding: 0.75rem 0;
  }

  .platform-feature-image {
    height: 120px;
  }

  .platform-feature-title {
    font-size: 1.2rem;
  }

  .platform-feature-description {
    font-size: 0.95rem;
    margin-bottom: 1.25rem;
  }
}
