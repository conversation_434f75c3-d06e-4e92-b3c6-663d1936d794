import './Features.css'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faChartLine,
  faCubes,
  faRobot,
  faShield,
  faPlug
} from '@fortawesome/free-solid-svg-icons'

function Features() {
  return (
    <section className="features-section">
      <div className="features-header">
        <h2 className="features-main-title">Why Teams Choose Celaeno Performance:</h2>
      </div>

      <div className="features-container">
        <div className="features-grid">
          {/* Feature Card 1 */}
          <div className="feature-card">
            <div className="feature-icon">
              <FontAwesomeIcon icon={faChartLine} size="2x" color="#FF6B35" />
            </div>
            <h3 className="feature-title">Data-driven insights that fuel better decisions</h3>
          </div>

          {/* Feature Card 2 */}
          <div className="feature-card">
            <div className="feature-icon">
              <FontAwesomeIcon icon={faCubes} size="2x" color="#FF6B35" />
            </div>
            <h3 className="feature-title">Modular platform - activate only what you need</h3>
          </div>

          {/* Feature Card 3 */}
          <div className="feature-card">
            <div className="feature-icon">
              <FontAwesomeIcon icon={faRobot} size="2x" color="#FF6B35" />
            </div>
            <h3 className="feature-title">Built-in AI assistant to guide HR workflows</h3>
          </div>

          {/* Feature Card 4 */}
          <div className="feature-card">
            <div className="feature-icon">
              <FontAwesomeIcon icon={faShield} size="2x" color="#FF6B35" />
            </div>
            <h3 className="feature-title">Enterprise-grade security and role-based access control</h3>
          </div>

          {/* Feature Card 5 */}
          <div className="feature-card">
            <div className="feature-icon">
              <FontAwesomeIcon icon={faPlug} size="2x" color="#FF6B35" />
            </div>
            <h3 className="feature-title">Integrates effortlessly with your HRIS, Slack, Teams, and more</h3>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Features
