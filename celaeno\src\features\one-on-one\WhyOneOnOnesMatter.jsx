import './WhyOneOnOnesMatter.css'

function WhyOneOnOnesMatter() {
  return (
    <section className="why-one-on-ones-matter">
      <div className="why-one-on-ones-container">
        <div className="why-one-on-ones-content">
          <h2 className="why-one-on-ones-title">Why 1:1s Matters</h2>
          <p className="why-one-on-ones-description">
            In high-performing teams, communication is everything. <br />
            Celaeno's 1:1 Meetings tool helps managers and employees 
            stay in sync, solve blockers faster, and build the kind of 
            rapport that fuels productivity and retention.
          </p>
          <p className="why-one-on-ones-subtitle">
            No more missed agendas or scattered notes — just 
            consistent, meaningful dialogue.
          </p>
        </div>       
        
      </div>
    </section>
  )
}

export default WhyOneOnOnesMatter
