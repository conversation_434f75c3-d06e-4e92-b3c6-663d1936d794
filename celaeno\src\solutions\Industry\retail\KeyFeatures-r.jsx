import '../../size/small/keyFeatures.css'

function KeyFeatures_r() {
  const features = [
    {
      feature: "Store-Specific Goal Templates",
      benefit: "Track key sales and service metrics by location"
    },
    {
      feature: "Mobile-Friendly 1:1 Frameworks",
      benefit: "Managers can run syncs on the floor"
    },
    {
      feature: "Quick-Review Forms",
      benefit: "Perfect for part-time and seasonal teams"
    },
    {
      feature: "Role-Based Feedback Loops",
      benefit: "Associate, cashier, and team lead focused"
    },
    {
      feature: "Centralized Performance Dashboards",
      benefit: "Multi-store visibility with roll-ups"
    }
  ]

  return (
    <section className="key-features">
      <div className='gradient-right'>
        
      </div>
      <div className="key-features-container">
        <h2 className="key-features-title">Retail-Ready Performance Features</h2>
        
        <div className="features-table">
          <div className="table-header">
            <div className="header-feature">Feature</div>
            <div className="header-benefit">Why It Works for Credit Unions</div>
          </div>
          
          <div className="table-body">
            {features.map((item, index) => (
              <div key={index} className="table-row">
                <div className="feature-cell">{item.feature}</div>
                <div className="benefit-cell">{item.benefit}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default KeyFeatures_r
