import '../../../features/one-on-one/OneOnOneHero.css'
import { useNavigate } from 'react-router-dom'

function RegionalHero() {
  const navigate = useNavigate()

  const handleGetStartedClick = () => {
    navigate('/pricing')
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }, 100)
  }

  return (
    <section className="one-on-one-hero">
      <div className="one-on-one-hero-container">
        <div className="one-on-one-hero-content">
          <h1 className="one-on-one-hero-title">
            Modern Performance Management <br />
            Rooted in Local Values

          </h1>
          <p className="one-on-one-hero-description">
            Regional and community banks are the backbone of local economies — and your people are your <br />
            greatest differentiator. Celaeno empowers your institution to build accountability, develop talent, <br />
            and meet regulatory standards — without losing the community-first culture that defines your brand.


          </p>
          <button className="one-on-one-hero-btn" onClick={handleGetStartedClick}>Get Started</button>
        </div>
      </div>
      
      {/* Curved Bottom */}
      <div className="one-on-one-hero-curve">
        <svg viewBox="0 0 1200 200" preserveAspectRatio="none">
          <path d="M0,0 Q600,200 1200,0 L1200,200 L0,200 Z" fill="white"/>
        </svg>
      </div>
    </section>
  )
}

export default RegionalHero
