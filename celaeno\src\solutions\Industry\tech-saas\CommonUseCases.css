.common-use-cases {
  padding: 4rem 0;
  background-color: #f1f1ec;
  position: relative;
}

.common-use-cases-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.common-use-cases-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #007474;
  text-align: center;
  margin-bottom: 3rem;
  line-height: 1.2;
}

.use-cases-grid-1 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.use-case-item {
  background: transparent;
  padding: 1.5rem;
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 1rem;
}

.use-case-item:hover {
  transform: translateY(-5px);
}

.use-case-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ff8c2b;
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.use-case-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.use-case-title {
  font-size: 1.1rem;
  font-weight: 700;
  color: #007474;
  margin-bottom: 0.8rem;
  line-height: 1.3;
  text-align: center;
}

.use-case-description-1 {
  font-size: 0.95rem;
  color: #666;
  font-weight: 600;
  line-height: 1.6;
  margin: 0;
  text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .common-use-cases {
    padding: 3rem 0;
  }
  
  .common-use-cases-container {
    padding: 0 1rem;
  }
  
  .common-use-cases-title {
    font-size: 2rem;
    margin-bottom: 2rem;
  }
  
  .use-cases-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
  
  .use-case-item {
    padding: 1.2rem;
    gap: 0.8rem;
  }

  .use-case-icon {
    font-size: 2rem;
  }
  
  .use-case-title {
    font-size: 1.1rem;
    margin-bottom: 0.6rem;
  }
  
  .use-case-description {
    font-size: 0.95rem;
  }
}

@media (max-width: 480px) {
  .use-cases-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .use-case-item {
    padding: 1rem;
    gap: 0.8rem;
  }

  .use-case-icon {
    font-size: 1.8rem;
  }

  .use-case-title {
    font-size: 1rem;
  }

  .use-case-description {
    font-size: 0.9rem;
  }
}
