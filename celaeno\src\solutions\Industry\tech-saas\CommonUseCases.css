.common-use-cases {
  padding: 4rem 0;
  background-color: #f8f9fa;
  position: relative;
}

.common-use-cases-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.common-use-cases-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #007474;
  text-align: center;
  margin-bottom: 3rem;
  line-height: 1.2;
}

.use-cases-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  max-width: 1000px;
  margin: 0 auto;
}

.use-case-item {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
}

.use-case-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.use-case-icon {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #ff8c2b, #ffab68);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.use-case-content {
  flex: 1;
}

.use-case-title {
  font-size: 1.2rem;
  font-weight: 700;
  color: #007474;
  margin-bottom: 0.8rem;
  line-height: 1.3;
}

.use-case-description {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .common-use-cases {
    padding: 3rem 0;
  }
  
  .common-use-cases-container {
    padding: 0 1rem;
  }
  
  .common-use-cases-title {
    font-size: 2rem;
    margin-bottom: 2rem;
  }
  
  .use-cases-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .use-case-item {
    padding: 1.5rem;
    gap: 1.2rem;
  }
  
  .use-case-icon {
    width: 50px;
    height: 50px;
    font-size: 1.3rem;
  }
  
  .use-case-title {
    font-size: 1.1rem;
    margin-bottom: 0.6rem;
  }
  
  .use-case-description {
    font-size: 0.95rem;
  }
}

@media (max-width: 480px) {
  .use-case-item {
    padding: 1.2rem;
    gap: 1rem;
  }
  
  .use-case-icon {
    width: 45px;
    height: 45px;
    font-size: 1.2rem;
  }
  
  .use-case-title {
    font-size: 1rem;
  }
  
  .use-case-description {
    font-size: 0.9rem;
  }
}
