import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faBullseye, faSync, faCompass, faComments } from '@fortawesome/free-solid-svg-icons'
import '../tech-saas/CommonUseCases.css'

function CommonUseCases_r() {
  const useCases = [
    {
      icon: faBullseye,
      title: "Set Store-Level Goals & KPIs",
      description: "Align your teams on targets like footfall conversion, basket size, and CSAT."
    },
    {
      icon: faSync,
      title: "Enable Feedback on the Floor",
      description: "Allow real-time peer and manager feedback via mobile devices."
    },
    {
      icon: faCompass,
      title: "Simplify Seasonal Staff Reviews",
      description: "Run quick, fair performance snapshots during seasonal peaks."
    },
    {
      icon: faComments,
      title: "Manager 1:1s & Coaching",
      description: "Provide structured check-ins and development plans for retail growth roles."
    }
  ]

  return (
    <section className="common-use-cases">
      <div className="common-use-cases-container">
        <h2 className="common-use-cases-title">Common Use Cases</h2>
        
        <div className="use-cases-grid-1">
          {useCases.map((useCase, index) => (
            <div key={index} className="use-case-item">
              <div className="use-case-icon">
                <FontAwesomeIcon icon={useCase.icon} />
              </div>
              <div className="use-case-content">
                <h3 className="use-case-title">{useCase.title}</h3>
                <p className="use-case-description-1">{useCase.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default CommonUseCases_r
