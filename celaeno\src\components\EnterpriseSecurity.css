/* Enterprise Security Section */
.enterprise-security {
  background: #007474;
  padding: 4rem 0;
  color: white;
}

.enterprise-security-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.enterprise-security-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  text-align: left;
  margin-bottom: 3rem;
  line-height: 1.2;
}

.security-features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem 10rem;
  max-width: 1000px;
}

.security-feature-item {
  background: #4fa6a4;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
}


.security-bullet {
  font-size: 1.5rem;
  color: white;
  font-weight: bold;
  flex-shrink: 0;
}

.security-text {
  font-size: 1.1rem;
  color: white;
  line-height: 1.4;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .enterprise-security {
    padding: 3rem 0;
  }

  .enterprise-security-title {
    font-size: 2rem;
    margin-bottom: 2.5rem;
    text-align: center;
  }

  .security-features-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    max-width: 100%;
  }

  .security-feature-item {
    padding: 1.25rem 1.5rem;
  }

  .security-text {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .enterprise-security {
    padding: 2.5rem 0;
  }

  .enterprise-security-title {
    font-size: 1.8rem;
    margin-bottom: 2rem;
  }

  .security-features-grid {
    gap: 1.25rem;
  }

  .security-feature-item {
    padding: 1rem 1.25rem;
  }

  .security-bullet {
    font-size: 1.25rem;
  }

  .security-text {
    font-size: 0.95rem;
  }
}
