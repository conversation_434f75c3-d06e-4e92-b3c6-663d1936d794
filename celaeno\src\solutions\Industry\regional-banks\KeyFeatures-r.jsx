import '../../size/small/keyFeatures.css'

function KeyFeatures_r() {
  const features = [
    {
      feature: "Location-Based Goal Management",
      benefit: "Align KPIs by branch, department, or role"
    },
    {
      feature: "Performance Review Templates",
      benefit: "Standardized forms for retail banking roles"
    },
    {
      feature: "Regulatory Goal Support",
      benefit: "Map and track goals aligned with regulatory mandates"
    },
    {
      feature: "Coaching Toolkit for Managers",
      benefit: "Ensure quality development conversations"
    },
    {
      feature: "Data Dashboards per Region",
      benefit: "Spot performance gaps and growth opportunities"
    }
  ]

  return (
    <section className="key-features">
      <div className='gradient-right'>
        
      </div>
      <div className="key-features-container">
        <h2 className="key-features-title">Key Features for Community Banks</h2>
        
        <div className="features-table">
          <div className="table-header">
            <div className="header-feature">Feature</div>
            <div className="header-benefit">Why It Works for Credit Unions</div>
          </div>
          
          <div className="table-body">
            {features.map((item, index) => (
              <div key={index} className="table-row">
                <div className="feature-cell">{item.feature}</div>
                <div className="benefit-cell">{item.benefit}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default KeyFeatures_r
