import './WhyChooseCelaeno.css'

function WhyChooseCelaeno() {
  return (
    <section className="why-choose-celaeno">
      <div className='gradient-left'>
        
      </div>
      <div className="why-choose-container">
        <div className="why-choose-content">
          <h2 className="why-choose-title">Why Small Businesses Choose Celaeno</h2>
          
          <p className="why-choose-intro">
            As a small business, you need software that's:
          </p>
          
          <div className="why-choose-features">
            <div className="feature-item">
              <h3>Easy to set up</h3>
            </div>
            <div className="feature-item">
              <h3>Affordable for your budget</h3>
            </div>
            <div className="feature-item">
              <h3>Powerful enough to scale</h3>
            </div>
            <div className="feature-item">
              <h3>Loved by teams, not just HR</h3>
            </div>
          </div>
          
          <p className="why-choose-conclusion">
            That's where <PERSON><PERSON><PERSON> comes in — a platform built for founders, lean HR teams, and fast-growing companies.
          </p>
        </div>
      </div>
    </section>
  )
}

export default WhyChooseCelaeno
