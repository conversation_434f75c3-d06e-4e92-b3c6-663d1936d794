import { useState } from 'react'
import './PricingHeader.css'

function PricingHeader() {
  const [billingCycle, setBillingCycle] = useState('monthly')

  return (
    <section className="pricing-header-section">
      <div className="curve-divider">
  <svg viewBox="0 0 1440 100" preserveAspectRatio="none">
  <path
    d="M0,20 Q720,40 1440,20 L1440,100 L0,100 Z"
    fill="#4fa6a4"
  />
</svg>

</div>

      <div className="pricing-header-container">
        <h1 className="pricing-header-title">Simple, Flexible Pricing for Every Team Size</h1>
        <p className="pricing-header-description">
          Whether you're a growing startup or an established enterprise, <PERSON><PERSON>eno has a plan that fits your 
          needs — with no hidden fees, ever.
        </p>
        
        <div className="billing-toggle">
          <button
            className={`toggle-btn ${billingCycle === 'monthly' ? 'active' : ''}`}
            onClick={() => setBillingCycle('monthly')}
          >
            Monthly
          </button>
          <button
            className={`toggle-btn ${billingCycle === 'yearly' ? 'active' : ''}`}
            onClick={() => setBillingCycle('yearly')}
          >
            Yearly
          </button>
        </div>

        {/* Pricing Cards */}
        <div className="pricing-cards">
          {/* Starter Plan */}
          <div className="pricing-card starter">
            <div className="card-content">
              <h3 className="card-title">Starter</h3>
              <p className="card-subtitle">Small Teams & Startups</p>
              <div className="price">
                <span className="price-amount">
                  {billingCycle === 'monthly' ? '$6/' : '$5/'}
                </span>
                <span className="price-period">
                  user<br />
                  {billingCycle === 'monthly' ? '(monthly)' : '(billed annually)'}
                </span>
              </div>
              <ul className="feature-list">
                <li className="feature-item-p">✔ 1:1 Meetings</li>
                <li className="feature-item-p">✔ Feedback</li>
                <li className="feature-item-p">✔ Goal Setting</li>
                <li className="feature-item-p">✔ Performance Reviews (Basic)</li>
                <li className="feature-item-p">✔ Email Support</li>
              </ul>
            </div>
            <button className="get-started-btn">Get Started</button>
          </div>

          {/* Growth Plan - Featured */}
          <div className="pricing-card growth featured">
            <div className="card-content">
              <h3 className="card-title">Growth</h3>
              <p className="card-subtitle">SMBs & Scaling Orgs</p>
              <div className="price">
                <span className="price-amount">
                  {billingCycle === 'monthly' ? '$12/' : '$10/'}
                </span>
                <span className="price-period">
                  user<br />
                  {billingCycle === 'monthly' ? '(monthly)' : '(billed annually)'}
                </span>
              </div>
              <div className="feature-intro">Everything in Starter, plus:</div>
              <ul className="feature-list">
                <li className="feature-item-p">✔ Personal Development Plans</li>
                <li className="feature-item-p">✔ Advanced Analytics</li>
                <li className="feature-item-p">✔ Custom Review Templates</li>
                <li className="feature-item-p">✔ Slack/MS Teams Integration</li>
                <li className="feature-item-p">✔ Chat Support</li>
              </ul>
            </div>
            <button className="get-started-btn">Get Started</button>
          </div>

          {/* Enterprise Plan */}
          <div className="pricing-card enterprise">
            <div className="card-content">
              <h3 className="card-title">Enterprise</h3>
              <p className="card-subtitle">Large Orgs & Enterprises</p>
              <div className="price">
                <span className="price-amount">Custom Quote</span>
              </div>
              <div className="feature-intro">Everything in Growth, plus:</div>
              <ul className="feature-list">
                <li className="feature-item-p">✔ Dedicated CSM</li>
                <li className="feature-item-p">✔ API Access & SSO</li>
                <li className="feature-item-p">✔ Compliance & Security Reports</li>
                <li className="feature-item-p">✔ Custom SLAs</li>
                <li className="feature-item-p">✔ Priority Support</li>
              </ul>
            </div>
            <button className="get-started-btn">Get Started</button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default PricingHeader
