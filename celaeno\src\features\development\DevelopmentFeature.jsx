import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { 
  faUsers, 
  faCalendarAlt, 
  faHistory, 
  faTasks, 
  faComments, 
  faRobot 
} from '@fortawesome/free-solid-svg-icons'
import { useNavigate } from 'react-router-dom'
import '../one-on-one/OneOnOneFeatures.css'

function DevelopmentFeatures() {
  const navigate = useNavigate()

  const handleGetStartedClick = () => {
    navigate('/signup')
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }, 100)
  }

  const features = [
    {
      icon: faUsers,
      title: "Custom Development Plans",
      description: "Create guided or open-ended plans tailored to roles, goals, or skill gaps."
    },
    {
      icon: faCalendarAlt,
      title: "Skill Gap Assessments",
      description: "Identify opportunities through assessments tied to competencies or job paths."
    },
    {
      icon: faHistory,
      title: "Learning Resource Integration",
      description: "Connect learning paths with Linkedin Learning, Udemy, or internal resources."
    },
    {
      icon: faTasks,
      title: "  Career Tracks & Role Pathways",
      description: "Help employees visualize how to level up within or across departments."
    },
    {
      icon: faComments,
      title: "Progress Check-ins & Manager Support",
      description: "Track milestones and createshared accountability between employee and manager."
    },
    {
      icon: faRobot,
      title: "Aspirations & Career Goals",
      description: "Let employees define where they want to go - and help them map the steps."
    }
  ]

  return (
    <section className="one-on-one-features">
      <div className="one-on-one-features-container">
        <h2 className="one-on-one-features-title">What You Can Do with Celaeno Personal Development</h2>
        <div className="one-on-one-features-grid">
          {features.map((feature, index) => (
            <div key={index} className="one-on-one-feature-card">
              <div className="one-on-one-feature-icon">
                <FontAwesomeIcon icon={feature.icon} />
              </div>
              <h3 className="one-on-one-feature-title">{feature.title}</h3>
              <p className="one-on-one-feature-description">{feature.description}</p>
            </div>
          ))}
          <div className="one-on-one-cta-container">
            <button className="one-on-one-cta-btn" onClick={handleGetStartedClick}>Get Started</button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default DevelopmentFeatures
