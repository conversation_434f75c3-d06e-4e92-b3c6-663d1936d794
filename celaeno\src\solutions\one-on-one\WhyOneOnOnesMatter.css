/* Why 1:1s Matter Section */
.why-one-on-ones-matter {
  padding: 6rem 0;
  background-color: white;
  position: relative;
  overflow: visible;
  z-index: 1000;
  margin-top: -40px;
  isolation: isolate;
}

.why-one-on-ones-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  gap: 4rem;
}



.why-one-on-ones-content {
  flex: 1;
  max-width: 600px;
  margin-top: -80px;
}

.why-one-on-ones-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #007474;
  margin-bottom: 2rem;
  line-height: 1.2;
}

.why-one-on-ones-description {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #333;
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.why-one-on-ones-subtitle {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #333;
  font-weight: 500;
}

.why-one-on-ones-visual {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  position: relative;
  min-height: 400px;
  overflow: hidden;
}

.overlapping-circles {
  position: relative;
  width: 100%;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  overflow: hidden;
}

.circle-light {
  position: absolute;
  width: 400px;
  height: 400px;
  background-color: #4fa6a4;
  border-radius: 50%;
  right: 100px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}

.circle-dark {
  position: absolute;
  width: 400px;
  height: 400px;
  background-color: #007474;
  border-radius: 50%;
  right: -100px;
  top: 57%;
  transform: translateY(-50%);
  z-index: 2;
}

/* Responsive Design */
@media (max-width: 968px) {
  .why-one-on-ones-container {
    flex-direction: column;
    text-align: center;
    gap: 3rem;
  }

  .why-one-on-ones-content {
    max-width: 100%;
  }

  .why-one-on-ones-visual {
    min-height: 300px;
  }

  .overlapping-circles {
    width: 300px;
    height: 300px;
  }

  .circle-light,
  .circle-dark {
    width: 220px;
    height: 220px;
  }
}

@media (max-width: 768px) {
  .why-one-on-ones-matter {
    padding: 4rem 0;
  }

  .why-one-on-ones-title {
    font-size: 2rem;
    margin-bottom: 1.5rem;
  }

  .why-one-on-ones-description,
  .why-one-on-ones-subtitle {
    font-size: 1rem;
  }

  .overlapping-circles {
    width: 250px;
    height: 250px;
  }

  .circle-light,
  .circle-dark {
    width: 180px;
    height: 180px;
  }
}

@media (max-width: 480px) {
  .why-one-on-ones-matter {
    padding: 3rem 0;
  }

  .why-one-on-ones-container {
    padding: 0 1rem;
  }

  .why-one-on-ones-title {
    font-size: 1.8rem;
  }

  .why-one-on-ones-visual {
    min-height: 250px;
  }

  .overlapping-circles {
    width: 200px;
    height: 200px;
  }

  .circle-light,
  .circle-dark {
    width: 140px;
    height: 140px;
  }
}
