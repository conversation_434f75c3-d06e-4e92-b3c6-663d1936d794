import '../../size/small/keyFeatures.css'

function KeyFeatures_f() {
  const features = [
    {
      feature: "Cross-Functional OKR Management",
      benefit: "Align compliance, tech, and ops in real time"
    },
    {
      feature: "Security + Compliance Review Templates",
      benefit: "Performance-linked risk visibility"
    },
    {
      feature: "Goal-Based Feedback",
      benefit: "Enable continuous improvement + trust loops"
    },
    {
      feature: "Development Plan Templates",
      benefit: "Upskill in-house talent instead of hiring gaps"
    },
    {
      feature: "Performance Alerts & Flags",
      benefit: "Monitor engagement, underperformance, risk"
    }
  ]

  return (
    <section className="key-features">
      <div className='gradient-right'>
        
      </div>
      <div className="key-features-container">
        <h2 className="key-features-title">Fintech-Ready Features</h2>
        
        <div className="features-table">
          <div className="table-header">
            <div className="header-feature">Feature</div>
            <div className="header-benefit">Why It Works for Credit Unions</div>
          </div>
          
          <div className="table-body">
            {features.map((item, index) => (
              <div key={index} className="table-row">
                <div className="feature-cell">{item.feature}</div>
                <div className="benefit-cell">{item.benefit}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default KeyFeatures_f
