/* Platform Hero Section */
.platform-hero {
  background: linear-gradient(135deg, #007474 0%, #005555 100%);
  min-height: 70vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  padding: 4rem 0;
}

.platform-hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
}

.platform-hero-content {
  text-align: left;
  max-width: 600px;
  margin-right: 20rem;
}

.platform-hero-title {
  font-size: 2.4rem;
  font-weight: 700;
  color: white;
  line-height: 1.2;
  margin-bottom: 1.5rem;
}

.platform-hero-description {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 2.5rem;
}

.platform-hero-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.platform-hero-btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

/* Background Decorative Circles */
.platform-hero-circle {
  position: absolute;
  border-radius: 50%;
  background: #4fa6a4;
  pointer-events: none;
}

.platform-hero-circle-left {
  width: 400px;
  height: 400px;
  top: -200px;
  left: -200px;
}

.platform-hero-circle-right {
  width: 500px;
  height: 500px;
  bottom: -250px;
  right: -250px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .platform-hero {
    min-height: 60vh;
    padding: 3rem 0;
  }

  .platform-hero-title {
    font-size: 2.5rem;
  }

  .platform-hero-description {
    font-size: 1.1rem;
  }

  .platform-hero-content {
    text-align: center;
  }

  .platform-hero-circle-left {
    width: 300px;
    height: 300px;
    top: -150px;
    left: -150px;
  }

  .platform-hero-circle-right {
    width: 350px;
    height: 350px;
    bottom: -175px;
    right: -175px;
  }
}

@media (max-width: 480px) {
  .platform-hero {
    min-height: 50vh;
    padding: 2rem 0;
  }

  .platform-hero-title {
    font-size: 2rem;
  }

  .platform-hero-description {
    font-size: 1rem;
  }

  .platform-hero-btn {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }

  .platform-hero-circle-left {
    width: 200px;
    height: 200px;
    top: -100px;
    left: -100px;
  }

  .platform-hero-circle-right {
    width: 250px;
    height: 250px;
    bottom: -125px;
    right: -125px;
  }
}
