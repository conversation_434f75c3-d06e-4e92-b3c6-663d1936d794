/* Header Styles */
.header {
  width: 100%;
  background-color: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

/* Brand Section */
.brand-section {
  flex: 1;
}

.logo-link {
  display: inline-block;
  text-decoration: none;
}

.logo {
  height: 40px;
  width: auto;
  transition: opacity 0.2s ease;
}

.logo:hover {
  opacity: 0.8;
}

/* Navigation Links */
.nav-links {
  display: flex;
  gap: 2rem;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.nav-link {
  text-decoration: none;
  color: #007474;
  font-weight: 500;
  font-size: 1rem;
  transition: color 0.2s ease;
}

.nav-link:hover {
  color: #005555;
}

.nav-link.active {
  color: #ff8c2b;
}

/* Dropdown Styles */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dropdown-arrow {
  font-size: 0.7rem;
  transition: transform 0.2s ease;
}

.dropdown:hover .dropdown-arrow {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: white;
  min-width: 200px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-radius: 0.5rem;
  padding: 0.5rem 0;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease;
  z-index: 1000;
}

.dropdown:hover .dropdown-menu,
.dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

/* Solutions Multi-level Dropdown */
.solutions-dropdown {
  width: 900px;
  padding: 0;
  display: flex;
  border-radius: 8px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  left: -200px;
  position: absolute;
}

.solutions-main-menu {
  width: 200px;
  background-color: #f8f9fa;
  border-radius: 8px 0 0 8px;
  padding: 1rem 0;
}

.solutions-category {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  font-size: 0.95rem;
  font-weight: 500;
  color: #333;
}

.solutions-category:hover,
.solutions-category.active {
  background-color: #e9ecef;
  color: #007474;
}

.submenu-arrow {
  font-size: 0.8rem;
  color: #666;
  transition: color 0.2s ease;
}

.solutions-category:hover .submenu-arrow,
.solutions-category.active .submenu-arrow {
  color: #007474;
}

.solutions-submenu {
  width: 700px;
  padding: 1.5rem;
  background-color: white;
  border-radius: 0 8px 8px 0;
  display: none;
}

.solutions-submenu.active {
  display: block;
}

/* Industry Grid Layout - Remove this as we're using the override below */

/* Industry grid item styling */
.submenu-items.industry-grid .submenu-item {
  padding: 0.75rem;
  border-radius: 6px;
  text-decoration: none;
  transition: background-color 0.2s ease;
  border: 1px solid transparent;
}

.submenu-items.industry-grid .submenu-item:hover {
  background-color: #f8f9fa;
}

.submenu-items.industry-grid .submenu-item-content h5 {
  font-size: 0.95rem;
  font-weight: 600;
  color: #007474;
  margin: 0 0 0.25rem 0;
}

.submenu-items.industry-grid .submenu-item-content p {
  font-size: 0.85rem;
  color: #666;
  margin: 0;
  line-height: 1.4;
}

.submenu-header {
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 1rem;
}

.submenu-header h4 {
  font-size: 1.2rem;
  font-weight: 700;
  color: #007474;
  margin: 0 0 0.5rem 0;
}

.submenu-header p {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
}

/* Default submenu items layout */
.submenu-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* Industry grid overrides default layout */
.submenu-items.industry-grid {
  display: grid !important;
  grid-template-columns: 1fr 1fr !important;
  grid-template-rows: repeat(3, auto) !important;
  gap: 1rem 2rem !important;
  flex-direction: unset !important;
}

.submenu-item {
  padding: 0.75rem;
  border-radius: 6px;
  text-decoration: none;
  transition: background-color 0.2s ease;
  border: 1px solid transparent;
}

.submenu-item:hover {
  background-color: #f8f9fa;
  border-color: #e9ecef;
}

.submenu-item-content h5 {
  font-size: 1rem;
  font-weight: 600;
  color: #007474;
  margin: 0 0 0.25rem 0;
}

.submenu-item-content p {
  font-size: 0.85rem;
  color: #666;
  margin: 0;
  line-height: 1.4;
}

/* Solutions Multi-level Dropdown */
.solutions-dropdown {
  width: 600px;
  display: flex;
  padding: 0;
}

.solutions-main-menu {
  width: 200px;
  background-color: #f8f9fa;
  border-right: 1px solid #e9ecef;
  padding: 1rem 0;
}

.solutions-category {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  cursor: pointer;
  border: none;
  background: none;
  color: #333;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.solutions-category:hover {
  background-color: #e9ecef;
  color: #007474;
}

.submenu-arrow {
  font-size: 0.8rem;
  color: #666;
}

.solutions-submenu {
  width: 400px;
  padding: 1.5rem;
  display: none;
  background-color: white;
}

.solutions-category:hover + .solutions-submenu,
.solutions-submenu:hover {
  display: block;
}

.solutions-category[data-submenu="industry"]:hover ~ #industry-submenu,
.solutions-category[data-submenu="company-size"]:hover ~ #company-size-submenu {
  display: block;
}

.submenu-header {
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 1rem;
}

.submenu-header h4 {
  font-size: 1.2rem;
  font-weight: 700;
  color: #007474;
  margin: 0 0 0.5rem 0;
}

.submenu-header p {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
}

/* Duplicate removed - using the one above with grid override */

.submenu-item {
  padding: 0.75rem;
  border-radius: 0.5rem;
  text-decoration: none;
  transition: background-color 0.2s ease;
}

.submenu-item:hover {
  background-color: #f8f9fa;
}

.submenu-item-content h5 {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.25rem 0;
}

.submenu-item-content p {
  font-size: 0.85rem;
  color: #666;
  margin: 0;
  line-height: 1.4;
}

.dropdown-item {
  display: block;
  padding: 0.75rem 1rem;
  color: #374151;
  text-decoration: none;
  font-size: 0.875rem;
  transition: background-color 0.2s ease;
}

.dropdown-item:hover {
  background-color: #f3f4f6;
  color: #007474;
}

.dropdown-item.active {
  color: #ff8c2b;
  background-color: #fff5f0;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.btn {
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.btn-contact {
  color: #007474;
  border: 1px solid #007474;
  border-radius: 1.5rem;
}

.btn-contact:hover {
  background-color: #007474;
  color: white;
}

.btn-login {
  color: #007474;
}

.btn-login:hover {
  color: #005555;
}

.btn-signup {
  background-color: #007474;
  color: white;
  border: 1px solid #007474;
  border-radius: 0.5rem;
  padding: 0.6rem 1.2rem;
}

.btn-signup:hover {
  background-color: #005555;
  border-color: #005555;
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    gap: 1rem;
  }

  .brand-section {
    display: none;
  }

  .nav-links {
    position: static;
    transform: none;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
  }

  .dropdown-menu {
    position: static;
    opacity: 1;
    visibility: visible;
    transform: none;
    box-shadow: none;
    background-color: #f9fafb;
    margin-top: 0.5rem;
    border-radius: 0.375rem;
  }

  .dropdown:hover .dropdown-menu {
    display: block;
  }

  .action-buttons {
    gap: 0.5rem;
  }

  .btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
  }
}
