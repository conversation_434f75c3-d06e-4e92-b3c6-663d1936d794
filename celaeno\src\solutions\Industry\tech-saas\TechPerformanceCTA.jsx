import { useNavigate } from 'react-router-dom'
import './TechPerformanceCTA.css'

function TechPerformanceCTA() {
  const navigate = useNavigate()

  const handleStartTrialClick = () => {
    navigate('/signup')
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }, 100)
  }

  const handleBookDemoClick = () => {
    navigate('/talk-to-sales')
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }, 100)
  }

  return (
    <section className="tech-performance-cta">
      <div className="tech-performance-cta-container">
        <div className="tech-performance-cta-content">
          <h2 className="tech-performance-cta-title">Tech Performance Without the Overhead</h2>
          
          <div className="tech-performance-cta-buttons">
            <button 
              className="start-trial-btn-1"
              onClick={handleStartTrialClick}
            >
              <span className="btn-main-text">Start Free Trial</span>
              <span className="btn-sub-text">Deploy OKRs, reviews, and feedback in days</span>
            </button>
            <button 
              className="book-demo-btn-1"
              onClick={handleBookDemoClick}
            >
              <span className="btn-main-text">Book a Demo</span>
              <span className="btn-sub-text">See how SaaS teams grow with Celaeno</span>
            </button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default TechPerformanceCTA
