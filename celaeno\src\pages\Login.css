/* Login Page */
.login-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f1f1ec;
}

.login-section {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem 0;
}

.login-container {
  width: 100%;
  max-width: 550px;
  padding: 0 2rem;
}

.login-form-wrapper {
  background-color: white;
  padding: 3rem;
  border-radius: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.login-logo {
  margin-bottom: 2rem;
}

.logo-image {
  height: 60px;
  width: auto;
}

.login-welcome {
  font-size: 2.5rem;
  font-weight: 700;
  color: #007474;
  margin: 0 0 2.5rem 0;
  line-height: 1.2;
}

.login-form {
  display: flex;
  flex-direction: column;
}

.form-group {
  margin-bottom: 1.5rem;
  text-align: left;
}

.form-input {
  width: 100%;
  padding: 1rem;
  border: 2px solid #007474;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-family: inherit;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
  background-color: white;
  color: #000;
}

.form-input::placeholder {
  color: #666;
  font-weight: 500;
}

.form-input:focus {
  outline: none;
  border-color: #005555;
}

.login-btn {
  background-color: #ffab68;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
  width: 100%;
  margin-bottom: 2rem;
}

.login-btn:hover {
  background-color: #ff9a4d;
}

.register-text {
  text-align: center;
  color: #666;
  font-size: 1rem;
  margin: 0;
}

.register-link {
  color: #007474;
  text-decoration: none;
  font-weight: 600;
}

.register-link:hover {
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
  .login-page {
    padding: 1rem 0;
  }

  .login-container {
    padding: 0 1rem;
  }

  .login-form-wrapper {
    padding: 2rem;
  }

  .logo-image {
    height: 50px;
  }

  .login-welcome {
    font-size: 2rem;
    margin-bottom: 2rem;
  }

  .form-input {
    padding: 0.875rem;
  }

  .login-btn {
    padding: 1rem;
    font-size: 1rem;
  }
}
