import './KeyFeatures.css'

function KeyFeatures() {
  const features = [
    {
      feature: "Departmental Goal Tracking",
      benefit: "Create OKRs by team, function, or region"
    },
    {
      feature: "Custom Review Cycles",
      benefit: "Quarterly, biannual, or ad-hoc — your cadence"
    },
    {
      feature: "Engagement & Sentiment Dashboards",
      benefit: "Spot trends across managers, teams, or locations"
    },
    {
      feature: "Manager Coaching Frameworks",
      benefit: "Help new leaders guide growth conversations"
    },
    {
      feature: "Talent Development Paths",
      benefit: "Turn top performers into future leaders"
    }
  ]

  return (
    <section className="key-features">
      <div className='gradient-right'>
        
      </div>
      <div className="key-features-container">
        <h2 className="key-features-title">Key Features for Small Businesses</h2>
        
        <div className="features-table">
          <div className="table-header">
            <div className="header-feature">Feature</div>
            <div className="header-benefit">Why It Works for Your Org</div>
          </div>
          
          <div className="table-body">
            {features.map((item, index) => (
              <div key={index} className="table-row">
                <div className="feature-cell">{item.feature}</div>
                <div className="benefit-cell">{item.benefit}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default KeyFeatures
