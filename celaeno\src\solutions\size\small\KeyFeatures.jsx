import './KeyFeatures.css'

function KeyFeatures() {
  const features = [
    {
      feature: "Plug-and-Play Templates",
      benefit: "Start with 1:1s, feedback, and goals in minutes"
    },
    {
      feature: "Automated Review Cycles",
      benefit: "No setup hassle — just set the date and go"
    },
    {
      feature: "Slack + Email Feedback",
      benefit: "Let employees give feedback where they work"
    },
    {
      feature: "AI-Powered Coaching Prompts",
      benefit: "Support managers in giving better guidance"
    },
    {
      feature: "Quick Setup (<1 Day)",
      benefit: "Launch in a morning, not a month"
    }
  ]

  return (
    <section className="key-features">
      <div className='gradient-right'>
        
      </div>
      <div className="key-features-container">
        <h2 className="key-features-title">Features Tailored for Small Teams</h2>
        
        <div className="features-table">
          <div className="table-header">
            <div className="header-feature">Feature</div>
            <div className="header-benefit">How It Helps Small Teams</div>
          </div>
          
          <div className="table-body">
            {features.map((item, index) => (
              <div key={index} className="table-row">
                <div className="feature-cell">{item.feature}</div>
                <div className="benefit-cell">{item.benefit}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default KeyFeatures
