import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import Home from './pages/Home'
import Platform from './pages/Platform'
import Resources from './pages/Resources'
import Pricing from './pages/Pricing'
import TalkToSales from './pages/TalkToSales'
import SignUp from './pages/SignUp'
import Login from './pages/Login'
import OneOnOnePage from './features/one-on-one/OneOnOnePage'
import FeedbackPage from './features/feedback/FeedbackPage'
import GoalsPage from './features/Goals/GoalsPage'
import ReviewPage from './features/review/ReviewPage'
import DevelopmentPage from './features/development/DevelopmentPage'
import SmallBusinessPage from './solutions/size/small/SmallBusinessPage'
import MediumSizedPage from './solutions/size/medium/MediumSizedPage'
import EnterprisePage from './solutions/size/enterprise/EnterprisePage'
import TechSaasPage from './solutions/Industry/tech-saas/TechSaasPage'
import CreditUnionsPage from './solutions/Industry/credit-unions/CreditUnionsPage'
import RegionalBanksPage from './solutions/Industry/regional-banks/RegionalBanksPage'
import RetailPage from './solutions/Industry/retail/RetailPage'
import FintechPage from './solutions/Industry/fintech/FintechPage'
import ManufacturingPage from './solutions/Industry/manufacturing/ManufacturingPage'
import './App.css'

function App() {
  return (
    <Router>
      <div className="app">
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/platform" element={<Platform />} />
          <Route path="/resources" element={<Resources />} />
          <Route path="/pricing" element={<Pricing />} />
          <Route path="/talk-to-sales" element={<TalkToSales />} />
          <Route path="/signup" element={<SignUp />} />
          <Route path="/login" element={<Login />} />
          <Route path="/features/one-on-one" element={<OneOnOnePage />} />
          <Route path="/features/feedback" element={<FeedbackPage />} />
          <Route path="/features/goals" element={<GoalsPage />} />
          <Route path="/features/review" element={<ReviewPage />} />
          <Route path="/features/development" element={<DevelopmentPage />} />
          <Route path="/solutions/small-business" element={<SmallBusinessPage />} />
          <Route path="/solutions/medium-sized" element={<MediumSizedPage />} />
          <Route path="/solutions/enterprise" element={<EnterprisePage />} />
          <Route path="/solutions/industry/tech-saas" element={<TechSaasPage />} />
          <Route path="/solutions/industry/credit-unions" element={<CreditUnionsPage />} />
          <Route path="/solutions/industry/regional-banks" element={<RegionalBanksPage />} />
          <Route path="/solutions/industry/retail" element={<RetailPage />} />
          <Route path="/solutions/industry/fintech" element={<FintechPage />} />
          <Route path="/solutions/industry/manufacturing" element={<ManufacturingPage />} />
        </Routes>
      </div>
    </Router>
  )
}

export default App
