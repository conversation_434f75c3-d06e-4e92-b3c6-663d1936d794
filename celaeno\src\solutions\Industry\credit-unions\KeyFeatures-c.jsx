import '../../size/small/keyFeatures.css'

function KeyFeatures_c() {
  const features = [
    {
      feature: "Multi-Location Review Cycles",
      benefit: "Manage performance across branches or HQ"
    },
    {
      feature: "Compliance Evaluation Templates",
      benefit: "Ensure process fidelity and service quality"
    },
    {
      feature: "Manager 1:1 Coaching Tools",
      benefit: "Standardize coaching conversations"
    },
    {
      feature: "Performance Dashboards",
      benefit: "Monitor engagement and performance per location"
    },
    {
      feature: "Documented Development Plans",
      benefit: "Track learning and compliance-related growth"
    }
  ]

  return (
    <section className="key-features">
      <div className='gradient-right'>
        
      </div>
      <div className="key-features-container">
        <h2 className="key-features-title">Key Features for Credit Unions</h2>
        
        <div className="features-table">
          <div className="table-header">
            <div className="header-feature">Feature</div>
            <div className="header-benefit">Why It Works for Credit Unions</div>
          </div>
          
          <div className="table-body">
            {features.map((item, index) => (
              <div key={index} className="table-row">
                <div className="feature-cell">{item.feature}</div>
                <div className="benefit-cell">{item.benefit}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default KeyFeatures_c
