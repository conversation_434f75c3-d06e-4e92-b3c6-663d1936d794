.key-features {
  padding: 4rem 0;
  background-color: white;
  position: relative;
}

.key-features-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.key-features-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #007474;
  text-align: center;
  margin-bottom: 3rem;
  line-height: 1.2;
}

.features-table {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.gradient-right {
  position: absolute;
  width: 600px;
  height: 600px;
  background: radial-gradient(circle, rgba(255, 171, 104, 0.5), transparent 70%);
  right: -25%;
  top: -12%;
  z-index: 2;
}

.table-header {
  display: grid;
  grid-template-columns: 1fr 1fr;
  background-color: #007474;
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
}

.header-feature,
.header-benefit {
  padding: 1.5rem 2rem;
  text-align: center;
}

.header-benefit {
  border-left: 1px solid rgba(255, 255, 255, 0.2);
}

.table-body {
  background-color: white;
}

.table-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  border-bottom: 1px solid #e5e7eb;
  transition: background-color 0.2s ease;
}

.table-row:hover {
  background-color: #f8f9fa;
}

.table-row:last-child {
  border-bottom: none;
}

.feature-cell,
.benefit-cell {
  padding: 1.5rem 2rem;
  font-size: 1rem;
  line-height: 1.5;
}

.feature-cell {
  font-weight: 600;
  color: #333;
  background-color: #f8f9fa;
  border-right: 1px solid #e5e7eb;
}

.benefit-cell {
  color: #666;
  background-color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .key-features {
    padding: 3rem 0;
  }
  
  .key-features-container {
    padding: 0 1rem;
  }
  
  .key-features-title {
    font-size: 2rem;
    margin-bottom: 2rem;
  }
  
  .table-header,
  .table-row {
    grid-template-columns: 1fr;
  }
  
  .header-benefit {
    border-left: none;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
  }
  
  .feature-cell {
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .header-feature,
  .header-benefit,
  .feature-cell,
  .benefit-cell {
    padding: 1rem 1.5rem;
    font-size: 0.95rem;
  }
  
  .table-row:hover .feature-cell {
    background-color: #f1f3f4;
  }
}
