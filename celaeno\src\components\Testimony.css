/* Testimony Section */
.testimony-section {
  background-color: white;
  padding: 0;
}

.testimony-header {
  background-color: white;
  padding: 4rem 0 2rem 0;
  text-align: center;
}

.testimony-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #007474;
  margin: 0;
  line-height: 1.2;
}

.testimony-content {
  background-color: #4fa6a4;
  padding: 4rem 0;
}

.testimony-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.testimony-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  align-items: stretch;
}

/* Testimony Cards */
.testimony-card {
  background-color: white;
  padding: 2rem 1.5rem;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 250px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.testimony-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.testimony-quote {
  flex-grow: 1;
  margin-bottom: 1.5rem;
}

.testimony-quote p {
  font-size: 1rem;
  color: #374151;
  line-height: 1.6;
  margin: 0;
  font-style: italic;
}

.testimony-author h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.testimony-author p {
  font-size: 0.9rem;
  color: #6b7280;
  margin: 0.125rem 0;
  line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .testimony-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
  
  .testimony-card {
    min-height: 220px;
  }
}

@media (max-width: 768px) {
  .testimony-header {
    padding: 3rem 0 1.5rem 0;
  }
  
  .testimony-title {
    font-size: 2rem;
  }
  
  .testimony-content {
    padding: 3rem 0;
  }
  
  .testimony-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .testimony-card {
    min-height: 200px;
    padding: 1.5rem 1.25rem;
  }
  
  .testimony-quote p {
    font-size: 0.95rem;
  }
}

@media (max-width: 480px) {
  .testimony-container {
    padding: 0 1rem;
  }
  
  .testimony-title {
    font-size: 1.75rem;
  }
  
  .testimony-card {
    padding: 1.25rem 1rem;
    min-height: 180px;
  }
  
  .testimony-quote p {
    font-size: 0.9rem;
  }
  
  .testimony-author h4 {
    font-size: 1rem;
  }
  
  .testimony-author p {
    font-size: 0.85rem;
  }
}
