import '../small/SampleUseCases.css'

function UseCases_m() {
  const useCases = [
    {
      title: "Tech & SaaS Scaleups",
      description: "Align engineering, GTM, and product teams with cross-functional OKRs."
    },
    {
      title: "Consulting & Services Firms",
      description: "Run structured reviews, feedback, and learning plans by team."
    },
    {
      title: "Growing Regional Enterprises",
      description: "Create consistency across multiple departments and locations."
    }
  ]

  return (
    <section className="sample-use-cases">
      <div className="sample-use-cases-container">
        <h2 className="sample-use-cases-title">Sample Use Cases</h2>
        
        <div className="use-cases-grid">
          {useCases.map((useCase, index) => (
            <div key={index} className="use-case-card">
              <div className="use-case-content">
                <h3 className="use-case-title">{useCase.title}</h3>
                <p className="use-case-description">{useCase.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default UseCases_m
