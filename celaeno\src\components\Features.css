/* Features Section */
.features-section {
  background-color: white;
  padding: 6rem 0;
  position: relative;
}

/* Gradient effect on left side with curve - full height */
.features-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 200px;
  height: 100%;
  background: linear-gradient(to left, transparent, rgba(255, 107, 53, 0.15));
  clip-path: ellipse(100% 100% at 0% 50%);
  pointer-events: none;
  z-index: 2;
}

/* Gradient effect on right side -*/
.features-section::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

/* Header Section */
.features-header {
  text-align: left;
  margin-bottom: 4rem;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding: 0 2rem;
}

.features-main-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #007474;
  margin: 0;
  line-height: 1.2;
}

.features-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1.5rem;
  align-items: start;
}

/* All 5 cards in one row */
.feature-card:nth-child(1) {
  grid-column: 1;
}

.feature-card:nth-child(2) {
  grid-column: 2;
}

.feature-card:nth-child(3) {
  grid-column: 3;
}

.feature-card:nth-child(4) {
  grid-column: 4;
}

.feature-card:nth-child(5) {
  grid-column: 5;
}

/* Feature Cards */
.feature-card {
  background-color: white;
  padding: 2rem 1.5rem;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid #e5e7eb;
  max-width: 280px;
  margin: 0 auto;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.feature-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin: 0 auto 1.5rem auto;
}

.feature-title {
  font-size: 1rem;
  font-weight: 600;
  color: #007474;
  line-height: 1.4;
  margin: 0 auto;
  max-width: 180px;
  height: 4.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
  
  .feature-card {
    padding: 2rem 1.5rem;
  }
}

@media (max-width: 768px) {
  .features-section {
    padding: 4rem 0;
  }

  .features-header {
    margin-bottom: 3rem;
    padding: 0 1rem;
  }

  .features-main-title {
    font-size: 2rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .feature-card {
    padding: 2rem 1.5rem;
  }
  
  .feature-icon {
    width: 70px;
    height: 70px;
    margin-bottom: 1rem;
  }
  
  .feature-title {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .features-main-title {
    font-size: 1.75rem;
  }

  .features-container {
    padding: 0 1rem;
  }
  
  .feature-card {
    padding: 1.5rem 1rem;
  }
  
  .feature-icon {
    width: 60px;
    height: 60px;
  }
}
