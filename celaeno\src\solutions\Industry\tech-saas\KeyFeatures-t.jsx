import '../../size/small/keyFeatures.css'

function KeyFeatures_t() {
  const features = [
    {
      feature: "OKR Management with Tags",
      benefit: "Set quarterly goals by product, sprint, or team"
    },
    {
      feature: "Cross-Functional Reviews",
      benefit: "Customize review templates by function"
    },
    {
      feature: "Slack + Jira + GitHub Sync",
      benefit: "Capture real-time updates, feedback, and commits"
    },
    {
      feature: "Feedback Inbox + AI Summary",
      benefit: "Prioritize and summarize incoming peer input"
    },
    {
      feature: "Customizable Engineering Metrics",
      benefit: "Map velocity, uptime, or PRs to goals"
    }
  ]

  return (
    <section className="key-features">
      <div className='gradient-right'>
        
      </div>
      <div className="key-features-container">
        <h2 className="key-features-title">Key Features for Community Banks</h2>
        
        <div className="features-table">
          <div className="table-header">
            <div className="header-feature">Feature</div>
            <div className="header-benefit">Why It Works for Credit Unions</div>
          </div>
          
          <div className="table-body">
            {features.map((item, index) => (
              <div key={index} className="table-row">
                <div className="feature-cell">{item.feature}</div>
                <div className="benefit-cell">{item.benefit}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default KeyFeatures_t
