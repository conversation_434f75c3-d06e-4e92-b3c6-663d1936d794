import { Link, useNavigate } from 'react-router-dom'
import './DetailedFeatures.css'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faUsers,
  faComments,
  faBullseye,
  faClipboardCheck,
  faBolt,
  faNetworkWired
} from '@fortawesome/free-solid-svg-icons'

function DetailedFeatures() {
  const navigate = useNavigate()

  const handlePlatformClick = (e) => {
    e.preventDefault()
    navigate('/platform')
    // Small delay to ensure navigation completes before scrolling
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }, 100)
  }

  const handleGetStartedClick = () => {
    navigate('/pricing')
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }, 100)
  }
  return (
    <section className="detailed-features-section">
      <div className="detailed-features-header">
        <h2 className="detailed-features-main-title">All-in-One Platform: Powerful Features Made Simple</h2>
        <p className="detailed-features-subtitle">Everything you need to manage performance, in one elegant workspace.</p>
      </div>

      <div className="detailed-features-container">
        <div className="detailed-features-grid">
          {/* Feature 1 - 1:1 Meetings */}
          <div className="detailed-feature-card">
            <div className="detailed-feature-icon">
              <FontAwesomeIcon icon={faUsers} size="2x" color="#FF6B35" />
            </div>
            <h3 className="detailed-feature-title">1:1 Meetings</h3>
            <p className="detailed-feature-description">
              Build stronger relationships through structured, recurring check-ins. Create agendas, take notes, and follow up — all in one place.
            </p>
            
          </div>

          {/* Feature 2 - Continuous Feedback */}
          <div className="detailed-feature-card">
            <div className="detailed-feature-icon">
              <FontAwesomeIcon icon={faComments} size="2x" color="#FF6B35" />
            </div>
            <h3 className="detailed-feature-title">Continuous Feedback</h3>
            <p className="detailed-feature-description">
              Celebrate wins and course-correct in real time. Enable peer-to-peer and manager feedback loops that encourage a culture of openness and collaboration.
            </p>
            
          </div>

          {/* Feature 3 - Goal Management */}
          <div className="detailed-feature-card">
            <div className="detailed-feature-icon">
              <FontAwesomeIcon icon={faBullseye} size="2x" color="#FF6B35" />
            </div>
            <h3 className="detailed-feature-title">Goal Management</h3>
            <p className="detailed-feature-description">
              Align everyone with strategic objectives using OKRs or SMART goals. Track progress, collaborate on goals, and drive accountability at every level.
            </p>
            
          </div>

          {/* Feature 4 - Performance Reviews & Assessments */}
          <div className="detailed-feature-card">
            <div className="detailed-feature-icon">
              <FontAwesomeIcon icon={faClipboardCheck} size="2x" color="#FF6B35" />
            </div>
            <h3 className="detailed-feature-title">Performance Reviews & Assessments</h3>
            <p className="detailed-feature-description">
              Conduct flexible, bias-free performance reviews. Choose from templates like 360° reviews, manager-only assessments, or self-reviews — all customizable.
            </p>
            
          </div>

          {/* Feature 5 - Personal Development Plans */}
          <div className="detailed-feature-card">
            <div className="detailed-feature-icon">
              <FontAwesomeIcon icon={faBolt} size="2x" color="#FF6B35" />
            </div>
            <h3 className="detailed-feature-title">Personal Development Plans</h3>
            <p className="detailed-feature-description">
              Help employees visualize their career path. Offer tailored learning goals, role benchmarks, and mentorship tracking to nurture growth.
            </p>
            
          </div>

          {/* Feature 6 - Everything is connected */}
          <div className="detailed-feature-card">
            <div className="detailed-feature-icon">
              <FontAwesomeIcon icon={faNetworkWired} size="2x" color="#FF6B35" />
            </div>
            <h3 className="detailed-feature-title">Everything is connected.</h3>
            <p className="detailed-feature-description">
              Each module works beautifully on its own — but when used together, they transform performance into a continuous, connected experience.
            </p>

          </div>
        </div>

        {/* Centered Get Started Button */}
        <div className="detailed-features-cta">
          <button className="get-started-btn-detailed" onClick={handleGetStartedClick}>Get Started</button>
        </div>
      </div>
    </section>
  )
}

export default DetailedFeatures
