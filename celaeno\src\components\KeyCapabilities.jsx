import './KeyCapabilities.css'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faLayerGroup, faBrain, faProjectDiagram, faPlug, faMobileAlt, faGlobeAmericas } from '@fortawesome/free-solid-svg-icons'

function KeyCapabilities() {
  const capabilities = [
    {
      icon: faLayerGroup,
      title: "Modular by Design",
      description: "Use only the features you need. Add more as your team grows."
    },
    {
      icon: faBrain,
      title: "AI-Powered Insights",
      description: "Make data-backed people decisions with smart analytics and suggestions."
    },
    {
      icon: faProjectDiagram,
      title: "Custom Workflows",
      description: "Build processes that mirror your org's unique rhythm."
    },
    {
      icon: faPlug,
      title: "Seamless Integrations",
      description: "Connect to tools you already use (Slack, Microsoft Teams, Google Workspace, HRIS platforms, etc.)."
    },
    {
      icon: faMobileAlt,
      title: "Mobile-Ready",
      description: "Manage performance anywhere, anytime — on any device."
    },
    {
      icon: faGlobeAmericas,
      title: "Global Scale",
      description: "Multi-language and timezone support for international teams."
    }
  ]

  return (
    <section className="key-capabilities">
      <div className="key-capabilities-container">
        <h2 className="key-capabilities-title">Key Capabilities</h2>
        
        <div className="capabilities-grid">
          {capabilities.map((capability, index) => (
            <div key={index} className="capability-item">
              <div className="capability-icon">
                <FontAwesomeIcon icon={capability.icon} />
              </div>
              <h3 className="capability-title">{capability.title}</h3>
              <p className="capability-description">{capability.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default KeyCapabilities
