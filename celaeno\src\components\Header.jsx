import React, { useState, useEffect, useRef } from 'react'
import { Link, useNavigate, useLocation } from 'react-router-dom'
import './Header.css'

function Header() {
  const navigate = useNavigate()
  const location = useLocation()
  const [activeSubmenu, setActiveSubmenu] = useState('industry')
  const [solutionsDropdownOpen, setSolutionsDropdownOpen] = useState(false)
  const [featuresDropdownOpen, setFeaturesDropdownOpen] = useState(false)
  const [resourcesDropdownOpen, setResourcesDropdownOpen] = useState(false)
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const dropdownRef = useRef(null)
  const featuresDropdownRef = useRef(null)
  const resourcesDropdownRef = useRef(null)
  const mobileMenuRef = useRef(null)

  // Detect mobile
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768)
  useEffect(() => {
    const handleResize = () => setIsMobile(window.innerWidth <= 768)
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setSolutionsDropdownOpen(false)
      }
      if (featuresDropdownRef.current && !featuresDropdownRef.current.contains(event.target)) {
        setFeaturesDropdownOpen(false)
      }
      if (resourcesDropdownRef.current && !resourcesDropdownRef.current.contains(event.target)) {
        setResourcesDropdownOpen(false)
      }
      if (mobileMenuRef.current && !mobileMenuRef.current.contains(event.target)) {
        setMobileMenuOpen(false)
      }
    }
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Close mobile menu on route change
  useEffect(() => {
    setMobileMenuOpen(false);
  }, [location.pathname]);

  const handleLogoClick = (e) => {
    e.preventDefault()
    navigate('/')
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }, 100)
  }

  const handlePlatformClick = (e) => {
    e.preventDefault()
    navigate('/platform')
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }, 100)
  }

  return (
    <header className="header">
      {/* Mobile Overlay */}
      {mobileMenuOpen && isMobile && (
        <div 
          className="mobile-overlay"
          onClick={() => setMobileMenuOpen(false)}
        ></div>
      )}
      <div className="header-container">
        {/* Topbar: Logo + Hamburger */}
        <div className="header-topbar">
          <div className="brand-section">
            <Link to="/" className="logo-link" onClick={handleLogoClick}>
              <svg width="202.93" height="40.79" viewBox="0 0 370.08333333333337 74.38832069738602" className="logo">
                <defs id="SvgjsDefs3921"></defs>
                <g id="SvgjsG3922" featureKey="symbolFeature-0" transform="matrix(0.9276928880976926,0,0,0.9276928880976926,-10.39016016975078,-9.204621769074619)" fill="#007474">
                  <path xmlns="http://www.w3.org/2000/svg" d="M29.5,25.5c2.5,0,4.6-2.1,4.6-4.6s-2.1-4.6-4.6-4.6c-2.5,0-4.6,2.1-4.6,4.6S27,25.5,29.5,25.5z M27,58  c0,0.7,0.6,1.3,1.3,1.3c0.7,0,1.3-0.6,1.3-1.3s-0.6-1.3-1.3-1.3C27.6,56.7,27,57.3,27,58z M36.2,31.3c1.1,0,1.9-0.9,1.9-1.9  c0-1.1-0.9-1.9-1.9-1.9c-1.1,0-1.9,0.9-1.9,1.9C34.3,30.4,35.1,31.3,36.2,31.3z M21.1,48.6c2.5,0,4.6-2.1,4.6-4.6  c0-2.5-2.1-4.6-4.6-4.6s-4.6,2.1-4.6,4.6C16.6,46.5,18.6,48.6,21.1,48.6z M14.5,37.8c1.1,0,1.9-0.9,1.9-1.9c0-1.1-0.9-1.9-1.9-1.9  c-1.1,0-1.9,0.9-1.9,1.9C12.6,37,13.4,37.8,14.5,37.8z M13.8,42c0-0.7-0.6-1.3-1.3-1.3c-0.7,0-1.3,0.6-1.3,1.3s0.6,1.3,1.3,1.3  C13.2,43.3,13.8,42.7,13.8,42z M19.9,31.2c1.8,0,3.2-1.4,3.2-3.2s-1.4-3.2-3.2-3.2c-1.8,0-3.2,1.4-3.2,3.2S18.1,31.2,19.9,31.2z   M58.8,13.6c0.7,0,1.3-0.6,1.3-1.3c0-0.7-0.6-1.3-1.3-1.3c-0.7,0-1.3,0.6-1.3,1.3C57.5,13,58.1,13.6,58.8,13.6z M23.8,57.5  c0-3.1-2.5-5.6-5.6-5.6c-3.1,0-5.6,2.5-5.6,5.6s2.5,5.6,5.6,5.6C21.3,63.1,23.8,60.6,23.8,57.5z M79.9,33.9c2.5,0,4.6-2.1,4.6-4.6  s-2.1-4.6-4.6-4.6s-4.6,2.1-4.6,4.6S77.3,33.9,79.9,33.9z M69.6,65.5c0,1.8,1.4,3.2,3.2,3.2c1.8,0,3.2-1.4,3.2-3.2s-1.4-3.2-3.2-3.2  C71,62.3,69.6,63.7,69.6,65.5z M72.8,22.9c1.8,0,3.2-1.4,3.2-3.2c0-1.8-1.4-3.2-3.2-3.2c-1.8,0-3.2,1.4-3.2,3.2  C69.6,21.5,71,22.9,72.8,22.9z M65.7,31.2c1.8,0,3.2-1.4,3.2-3.2s-1.4-3.2-3.2-3.2c-1.8,0-3.2,1.4-3.2,3.2S63.9,31.2,65.7,31.2z   M77.2,42.5c0,3.1,2.5,5.6,5.6,5.6c3.1,0,5.6-2.5,5.6-5.6s-2.5-5.6-5.6-5.6C79.7,36.9,77.2,39.4,77.2,42.5z M74,42  c0-0.7-0.6-1.3-1.3-1.3c-0.7,0-1.3,0.6-1.3,1.3s0.6,1.3,1.3,1.3C73.4,43.3,74,42.7,74,42z M64.8,16.1c1.1,0,1.9-0.9,1.9-1.9  s-0.9-1.9-1.9-1.9c-1.1,0-1.9,0.9-1.9,1.9S63.8,16.1,64.8,16.1z M70.8,37.8c1.1,0,1.9-0.9,1.9-1.9c0-1.1-0.9-1.9-1.9-1.9  s-1.9,0.9-1.9,1.9C68.9,37,69.8,37.8,70.8,37.8z M31.4,34.5c0-1.8-1.4-3.2-3.2-3.2c-1.8,0-3.2,1.4-3.2,3.2s1.4,3.2,3.2,3.2  C30,37.7,31.4,36.3,31.4,34.5z M64.8,68.7c-1.1,0-1.9,0.9-1.9,1.9c0,1.1,0.9,1.9,1.9,1.9c1.1,0,1.9-0.9,1.9-1.9  C66.7,69.6,65.9,68.7,64.8,68.7z M58.3,76.4c-3.1,0-5.6,2.5-5.6,5.6s2.5,5.6,5.6,5.6c3.1,0,5.6-2.5,5.6-5.6S61.4,76.4,58.3,76.4z   M71.5,74.5c-2.5,0-4.6,2.1-4.6,4.6s2.1,4.6,4.6,4.6c2.5,0,4.6-2.1,4.6-4.6S74,74.5,71.5,74.5z M57.5,72.5c0,0.7,0.6,1.3,1.3,1.3  c0.7,0,1.3-0.6,1.3-1.3c0-0.7-0.6-1.3-1.3-1.3C58.1,71.3,57.5,71.8,57.5,72.5z M88.5,56.7c-0.7,0-1.3,0.6-1.3,1.3s0.6,1.3,1.3,1.3  c0.7,0,1.3-0.6,1.3-1.3S89.1,56.7,88.5,56.7z M86.5,62.2c-1.1,0-1.9,0.9-1.9,1.9c0,1.1,0.9,1.9,1.9,1.9c1.1,0,1.9-0.9,1.9-1.9  C88.4,63,87.6,62.2,86.5,62.2z M56.2,25.5c2.5,0,4.6-2.1,4.6-4.6s-2.1-4.6-4.6-4.6s-4.6,2.1-4.6,4.6S53.7,25.5,56.2,25.5z   M79.9,51.4c-2.5,0-4.6,2.1-4.6,4.6c0,2.5,2.1,4.6,4.6,4.6s4.6-2.1,4.6-4.6C84.4,53.5,82.4,51.4,79.9,51.4z M21.1,66.1  c-2.5,0-4.6,2.1-4.6,4.6s2.1,4.6,4.6,4.6s4.6-2.1,4.6-4.6S23.7,66.1,21.1,66.1z M81.1,68.8c-1.8,0-3.2,1.4-3.2,3.2s1.4,3.2,3.2,3.2  c1.8,0,3.2-1.4,3.2-3.2S82.9,68.8,81.1,68.8z M30.2,62.2c-1.1,0-1.9,0.9-1.9,1.9c0,1.1,0.9,1.9,1.9,1.9s1.9-0.9,1.9-1.9  C32.1,63,31.2,62.2,30.2,62.2z M35.3,68.8c-1.8,0-3.2,1.4-3.2,3.2s1.4,3.2,3.2,3.2c1.8,0,3.2-1.4,3.2-3.2S37.1,68.8,35.3,68.8z   M36.2,83.9c-1.1,0-1.9,0.9-1.9,1.9s0.9,1.9,1.9,1.9c1.1,0,1.9-0.9,1.9-1.9S37.2,83.9,36.2,83.9z M28.2,77.1c-1.8,0-3.2,1.4-3.2,3.2  c0,1.8,1.4,3.2,3.2,3.2c1.8,0,3.2-1.4,3.2-3.2C31.4,78.5,30,77.1,28.2,77.1z M41,27.5c0,0.7,0.6,1.3,1.3,1.3c0.7,0,1.3-0.6,1.3-1.3  c0-0.7-0.6-1.3-1.3-1.3C41.5,26.2,41,26.8,41,27.5z M48.3,17.9c0-3.1-2.5-5.6-5.6-5.6c-3.1,0-5.6,2.5-5.6,5.6s2.5,5.6,5.6,5.6  C45.8,23.6,48.3,21.1,48.3,17.9z M42.2,86.4c-0.7,0-1.3,0.6-1.3,1.3c0,0.7,0.6,1.3,1.3,1.3c0.7,0,1.3-0.6,1.3-1.3  C43.5,87,42.9,86.4,42.2,86.4z M44.8,74.5c-2.5,0-4.6,2.1-4.6,4.6s2.1,4.6,4.6,4.6s4.6-2.1,4.6-4.6S47.3,74.5,44.8,74.5z"></path>
                </g>
                <g id="SvgjsG3923" featureKey="nameFeature-0" transform="matrix(1.269626678698065,0,0,1.269626678698065,89.95289494193584,-11.782134028480789)" fill="#007474">
                  <path d="M29.96 14 l-2.8 3.28 q-1.64 -1.68 -3.92 -2.64 t-4.8 -0.96 q-3.28 0 -5.98 1.48 t-4.22 4.04 q-1.56 2.68 -1.56 5.96 q0 3.12 1.68 5.76 q1.6 2.48 4.3 3.94 t5.82 1.46 q2.88 0 5.36 -1.24 t4.12 -3.44 l2.68 3.64 q-2.28 2.56 -5.46 3.98 t-6.74 1.42 q-4.44 0 -8.16 -2.16 q-3.64 -2.08 -5.72 -5.72 q-2.16 -3.72 -2.16 -8.16 q0 -4.28 2.28 -7.88 q2.16 -3.44 5.86 -5.46 t7.94 -2.02 q3.32 0 6.32 1.28 q2.92 1.2 5.16 3.44 z M41.4 28.04 l0 7.6 l18.4 0 l0 4.36 l-22.76 0 l0 -29.84 l21 0 l0 4.4 l-16.64 0 l0 9.04 q1.24 -0.72 2.76 -0.96 q1.12 -0.2 2.88 -0.2 z M87.72 40 l-20.92 0 l0 -29.84 l4.36 0 l0 25.48 l16.56 0 l0 4.36 z M102.24000000000001 13.32 l-1.64 -3.68 l4.8 0 l14.36 30.36 l-4.88 0 l-1.96 -4.36 l-9.08 0 q-2.56 0 -4.6 0.48 q-1.8 0.44 -3 1.16 q-1.08 0.64 -1.44 1.32 l-0.76 1.4 l-4.72 0 z M105.44000000000001 31.240000000000002 l5.52 0 l-6.52 -12.6 l-6.6 14.28 q0.88 -0.76 2.68 -1.2 q2.04 -0.48 4.92 -0.48 z M129.72 28.04 l0 7.6 l18.4 0 l0 4.36 l-22.76 0 l0 -29.84 l21 0 l0 4.4 l-16.64 0 l0 9.04 q1.24 -0.72 2.76 -0.96 q1.12 -0.2 2.88 -0.2 l6.52 0 l0 4.44 l-6.52 0 q-1.64 0 -2.88 0.2 q-1.52 0.28 -2.76 0.96 z M156 14.36 l-3.48 -4.2 l5.64 0 l19.16 22.8 l0 -22.8 l4.4 0 l0 29.84 l-4.32 0 l-17.12 -20.52 l0 20.52 l-4.28 0 l0 -25.64 z M216.32 24.880000000000003 q0 -3.2 -1.64 -5.8 q-1.56 -2.52 -4.26 -3.96 t-5.86 -1.44 q-3.28 0 -5.98 1.48 t-4.22 4.04 q-1.56 2.68 -1.56 5.96 q0 3.12 1.68 5.76 q1.6 2.48 4.3 3.94 t5.84 1.46 t5.84 -1.52 t4.26 -4.08 q1.6 -2.68 1.6 -5.84 z M220.64000000000001 25.119999999999997 q0 4.32 -2.24 7.96 q-2.16 3.48 -5.86 5.54 t-7.98 2.06 q-4.44 0 -8.16 -2.16 q-3.64 -2.08 -5.72 -5.72 q-2.16 -3.72 -2.16 -8.16 q0 -4.28 2.28 -7.88 q2.16 -3.44 5.86 -5.46 t7.94 -2.02 q4.36 0 8.08 2.12 q3.64 2.08 5.76 5.64 q2.2 3.72 2.2 8.08 z"></path>
                </g>
                <g id="SvgjsG3924" featureKey="nameFeature-1" transform="matrix(0.7809564342347604,0,0,0.7809564342347604,89.56379161488924,42.752725243617384)" fill="#007474">
                  <path d="M14.44 22.92 l5.28 0 q4.4 0 4.4 -4.24 q0 -2.2 -1.1 -3.18 t-3.3 -0.98 l-10.96 0 l0 9.6 q1.24 -0.72 2.8 -1 q1.16 -0.2 2.88 -0.2 z M8.76 40 l-4.36 0 l0 -29.84 l15.32 0 q2.64 0 4.6 1.02 t3.04 2.94 t1.08 4.54 t-1.08 4.58 t-3.06 3.02 t-4.58 1.06 l-5.28 0 q-3.44 0 -5.68 1.2 l0 11.48 z M39.2 28.04 l0 7.6 l18.4 0 l0 4.36 l-22.76 0 l0 -29.84 l21 0 l0 4.4 l-16.64 0 l0 9.04 q1.24 -0.72 2.76 -0.96 q1.12 -0.2 2.88 -0.2 l6.52 0 l0 4.44 l-6.52 0 q-1.64 0 -2.88 0.2 q-1.52 0.28 -2.76 0.96 z M68.96000000000001 14.52 l0 9.92 q1.24 -0.72 2.76 -0.96 q1.12 -0.2 2.88 -0.2 l5.68 0 q2.36 0 3.68 -1.08 q1.4 -1.16 1.4 -3.48 q0 -2.2 -1.48 -3.28 q-1.28 -0.92 -3.6 -0.92 l-11.32 0 z M81.72 35.28 l-3.64 -7.64 l-3.48 0 q-1.64 0 -2.88 0.2 q-1.52 0.28 -2.76 0.96 l0 11.2 l-4.36 0 l0 -29.84 l15.68 0 q2.76 0 4.88 1.04 t3.28 2.96 q1.24 2.04 1.24 4.8 q0 3.36 -1.8 5.52 t-5.08 3 l2.32 4.76 q1 1.48 1.56 2.08 q0.68 0.76 1.3 1.02 t1.62 0.26 l0.44 0 l0.96 -0.04 l0 4.44 q-2.32 0 -3.44 -0.16 q-1.88 -0.32 -3.2 -1.24 q-1.56 -1.12 -2.64 -3.32 z M102.56000000000002 28.04 l0 11.96 l-4.36 0 l0 -29.84 l21 0 l0 4.4 l-16.64 0 l0 9.04 q1.24 -0.72 2.76 -0.96 q1.12 -0.2 2.88 -0.2 l6.52 0 l0 4.44 l-6.52 0 q-1.64 0 -2.88 0.2 q-1.52 0.28 -2.76 0.96 z M151.8 24.880000000000003 q0 -3.2 -1.64 -5.8 q-1.56 -2.52 -4.26 -3.96 t-5.86 -1.44 q-3.28 0 -5.98 1.48 t-4.22 4.04 q-1.56 2.68 -1.56 5.96 q0 3.12 1.68 5.76 q1.6 2.48 4.3 3.94 t5.84 1.46 t5.84 -1.52 t4.26 -4.08 q1.6 -2.68 1.6 -5.84 z M156.12 25.119999999999997 q0 4.32 -2.24 7.96 q-2.16 3.48 -5.86 5.54 t-7.98 2.06 q-4.44 0 -8.16 -2.16 q-3.64 -2.08 -5.72 -5.72 q-2.16 -3.72 -2.16 -8.16 q0 -4.28 2.28 -7.88 q2.16 -3.44 5.86 -5.46 t7.94 -2.02 q4.36 0 8.08 2.12 q3.64 2.08 5.76 5.64 q2.2 3.72 2.2 8.08 z M167.28 14.52 l0 9.92 q1.24 -0.72 2.76 -0.96 q1.12 -0.2 2.88 -0.2 l5.68 0 q2.36 0 3.68 -1.08 q1.4 -1.16 1.4 -3.48 q0 -2.2 -1.48 -3.28 q-1.28 -0.92 -3.6 -0.92 l-11.32 0 z M180.04000000000002 35.28 l-3.64 -7.64 l-3.48 0 q-1.64 0 -2.88 0.2 q-1.52 0.28 -2.76 0.96 l0 11.2 l-4.36 0 l0 -29.84 l15.68 0 q2.76 0 4.88 1.04 t3.28 2.96 q1.24 2.04 1.24 4.8 q0 3.36 -1.8 5.52 t-5.08 3 l2.32 4.76 q1 1.48 1.56 2.08 q0.68 0.76 1.3 1.02 t1.62 0.26 l0.44 0 l0.96 -0.04 l0 4.44 q-2.32 0 -3.44 -0.16 q-1.88 -0.32 -3.2 -1.24 q-1.56 -1.12 -2.64 -3.32 z M222 19.48 l-10.2 12.2 l-10.12 -12.2 l0 20.52 l-4.28 0 l0 -25.64 l-3.48 -4.2 l5.64 0 l12.2 14.64 l12.36 -14.64 l5.64 0 l-3.48 4.2 l0 25.64 l-4.28 0 l0 -20.52 z M245.28 13.32 l-1.64 -3.68 l4.8 0 l14.36 30.36 l-4.88 0 l-1.96 -4.36 l-9.08 0 q-2.56 0 -4.6 0.48 q-1.8 0.44 -3 1.16 q-1.08 0.64 -1.44 1.32 l-0.76 1.4 l-4.72 0 z M248.48000000000002 31.240000000000002 l5.52 0 l-6.52 -12.6 l-6.6 14.28 q0.88 -0.76 2.68 -1.2 q2.04 -0.48 4.92 -0.48 z M269.28 14.36 l-3.48 -4.2 l5.64 0 l19.16 22.8 l0 -22.8 l4.4 0 l0 29.84 l-4.32 0 l-17.12 -20.52 l0 20.52 l-4.28 0 l0 -25.64 z M329.35999999999996 14 l-2.8 3.28 q-1.64 -1.68 -3.92 -2.64 t-4.8 -0.96 q-3.28 0 -5.98 1.48 t-4.22 4.04 q-1.56 2.68 -1.56 5.96 q0 3.12 1.68 5.76 q1.6 2.48 4.3 3.94 t5.82 1.46 q2.88 0 5.36 -1.24 t4.12 -3.44 l2.68 3.64 q-2.28 2.56 -5.46 3.98 t-6.74 1.42 q-4.44 0 -8.16 -2.16 q-3.64 -2.08 -5.72 -5.72 q-2.16 -3.72 -2.16 -8.16 q0 -4.28 2.28 -7.88 q2.16 -3.44 5.86 -5.46 t7.94 -2.02 q3.32 0 6.32 1.28 q2.92 1.2 5.16 3.44 z M340.79999999999995 28.04 l0 7.6 l18.4 0 l0 4.36 l-22.76 0 l0 -29.84 l21 0 l0 4.4 l-16.64 0 l0 9.04 q1.24 -0.72 2.76 -0.96 q1.12 -0.2 2.88 -0.2 l6.52 0 l0 4.44 l-6.52 0 q-1.64 0 -2.88 0.2 q-1.52 0.28 -2.76 0.96 z"></path>
                </g>
              </svg>
            </Link>
          </div>
          {/* Hamburger only on mobile and only when menu is not open */}
          {isMobile && !mobileMenuOpen && (
            <button 
              className="mobile-menu-toggle"
              onClick={() => setMobileMenuOpen(true)}
              aria-label="Open mobile menu"
            >
              <span className="hamburger-line"></span>
              <span className="hamburger-line"></span>
              <span className="hamburger-line"></span>
            </button>
          )}
        </div>
        {/* Nav and buttons: desktop always, mobile only if open */}
        {(!isMobile || mobileMenuOpen) && (
          <div
            className={`header-mobile-menu${mobileMenuOpen ? ' open' : ''}`}
            ref={mobileMenuRef}
            style={{ display: !isMobile ? 'flex' : undefined }}
          >
            {mobileMenuOpen && (
              <button
                className="mobile-menu-close"
                onClick={() => setMobileMenuOpen(false)}
                aria-label="Close mobile menu"
              >
                <span style={{fontSize: '2.5rem', color: '#007474', fontWeight: 700, lineHeight: 1}}>×</span>
              </button>
            )}
            <nav className={`nav-links${mobileMenuOpen ? ' mobile-open' : ''}`}>
              <Link
                to="/platform"
                className={`nav-link ${location.pathname === '/platform' ? 'active' : ''}`}
                onClick={handlePlatformClick}
              >
                Platform
              </Link>

              {/* Features with Dropdown */}
              <div className={`dropdown ${featuresDropdownOpen ? 'open' : ''}`} ref={featuresDropdownRef}>
                <a
                  href="#features"
                  className={`nav-link dropdown-toggle ${location.pathname.startsWith('/features') ? 'active' : ''}`}
                  onClick={(e) => {
                    e.preventDefault()
                    setFeaturesDropdownOpen(!featuresDropdownOpen)
                  }}
                >
                  Features
                  <span className="dropdown-arrow">▼</span>
                </a>
                <div className={`dropdown-menu ${featuresDropdownOpen ? 'show' : ''}`}>
                  <Link
                    to="/features/one-on-one"
                    className={`dropdown-item ${location.pathname === '/features/one-on-one' ? 'active' : ''}`}
                  >
                    1:1 Meetings
                  </Link>
                  <Link
                    to="/features/feedback"
                    className={`dropdown-item ${location.pathname === '/features/feedback' ? 'active' : ''}`}
                  >
                    Feedback
                  </Link>
                  <Link
                    to="/features/goals"
                    className={`dropdown-item ${location.pathname === '/features/goals' ? 'active' : ''}`}
                  >
                    Goals & OKRs
                  </Link>
                  <Link
                    to="/features/review"
                    className={`dropdown-item ${location.pathname === '/features/review' ? 'active' : ''}`}
                  >
                    Performance Reviews
                  </Link>
                  <Link
                    to="/features/development"
                    className={`dropdown-item ${location.pathname === '/features/development' ? 'active' : ''}`}
                  >
                    Personal Development
                  </Link>
                </div>
              </div>

              {/* Solutions with Multi-level Dropdown */}
              <div className={`dropdown ${solutionsDropdownOpen ? 'open' : ''}`} ref={dropdownRef}>
                <a
                  href="#solutions"
                  className={`nav-link dropdown-toggle ${location.pathname.startsWith('/solutions') ? 'active' : ''}`}
                  onClick={(e) => {
                    e.preventDefault()
                    setSolutionsDropdownOpen(!solutionsDropdownOpen)
                  }}
                >
                  Solutions
                  <span className="dropdown-arrow">▼</span>
                </a>
                <div className={`dropdown-menu solutions-dropdown ${solutionsDropdownOpen ? 'show' : ''}`}>
                  <div className="solutions-main-menu">
                    <div
                      className={`dropdown-item solutions-category ${activeSubmenu === 'industry' ? 'active' : ''}`}
                      onClick={() => setActiveSubmenu('industry')}
                    >
                      <span>Industry</span>
                      <span className="submenu-arrow"> →</span>
                    </div>
                    <div
                      className={`dropdown-item solutions-category ${activeSubmenu === 'company-size' ? 'active' : ''}`}
                      onClick={() => setActiveSubmenu('company-size')}
                    >
                      <span>Company size</span>
                      <span className="submenu-arrow"> →</span>
                    </div>
                  </div>

                  {/* Industry Submenu */}
                  <div className={`solutions-submenu ${activeSubmenu === 'industry' ? 'active' : ''}`} id="industry-submenu">
                    <div className="submenu-header">
                      <h4>Industry</h4>
                      <p>How organizations in your field use Celaeno.</p>
                    </div>
                    <div className="submenu-items industry-grid">
                      <div className="industry-column">
                        <Link to="/solutions/industry/tech-saas" className={`submenu-item ${location.pathname === '/solutions/industry/tech-saas' ? 'active' : ''}`}>
                          <div className="submenu-item-content">
                            <h5>Tech & SaaS</h5>
                            <p>Scale fast with aligned, empowered teams.</p>
                          </div>
                        </Link>
                        <Link to="/solutions/industry/credit-unions" className={`submenu-item ${location.pathname === '/solutions/industry/credit-unions' ? 'active' : ''}`}>
                          <div className="submenu-item-content">
                            <h5>Credit Unions</h5>
                            <p>Trust-first tools for growing teams.</p>
                          </div>
                        </Link>
                        <Link to="/solutions/industry/regional-banks" className={`submenu-item ${location.pathname === '/solutions/industry/regional-banks' ? 'active' : ''}`}>
                          <div className="submenu-item-content">
                            <h5>Regional & Community Banks</h5>
                            <p>Strengthen teams, serve communities better.</p>
                          </div>
                        </Link>
                      </div>
                      <div className="industry-column">
                        <Link to="/solutions/industry/retail" className={`submenu-item ${location.pathname === '/solutions/industry/retail' ? 'active' : ''}`}>
                          <div className="submenu-item-content">
                            <h5>Retail</h5>
                            <p>Empower frontline staff, elevate service.</p>
                          </div>
                        </Link>
                        <Link to="/solutions/industry/fintech" className={`submenu-item ${location.pathname === '/solutions/industry/fintech' ? 'active' : ''}`}>
                          <div className="submenu-item-content">
                            <h5>Fintech</h5>
                            <p>Fast teams need faster feedback.</p>
                          </div>
                        </Link>
                        <Link to="/solutions/industry/manufacturing" className={`submenu-item ${location.pathname === '/solutions/industry/manufacturing' ? 'active' : ''}`}>
                          <div className="submenu-item-content">
                            <h5>Manufacturing</h5>
                            <p>Support the people behind production.</p>
                          </div>
                        </Link>
                      </div>
                    </div>
                  </div>

                  {/* Company Size Submenu */}
                  <div className={`solutions-submenu ${activeSubmenu === 'company-size' ? 'active' : ''}`} id="company-size-submenu">
                    <div className="submenu-header">
                      <h4>Company size</h4>
                      <p>How organizations of your size use Celaeno.</p>
                    </div>
                    <div className="submenu-items">
                      <Link to="/solutions/small-business" className={`submenu-item ${location.pathname === '/solutions/small-business' ? 'active' : ''}`}>
                        <div className="submenu-item-content">
                          <h5>Small Business</h5>
                          <p>Perfect for growing teams of 1-50 employees.</p>
                        </div>
                      </Link>
                      <Link to="/solutions/medium-sized" className={`submenu-item ${location.pathname === '/solutions/medium-sized' ? 'active' : ''}`}>
                        <div className="submenu-item-content">
                          <h5>Medium-sized</h5>
                          <p>Ideal for established companies of 51-500 employees.</p>
                        </div>
                      </Link>
                      <Link to="/solutions/enterprise" className={`submenu-item ${location.pathname === '/solutions/enterprise' ? 'active' : ''}`}>
                        <div className="submenu-item-content">
                          <h5>Enterprise</h5>
                          <p>Comprehensive solutions for 500+ employee organizations.</p>
                        </div>
                      </Link>
                    </div>
                  </div>
                </div>
              </div>

              {/* Resources with Dropdown */}
              <div className={`dropdown ${resourcesDropdownOpen ? 'open' : ''}`} ref={resourcesDropdownRef}>
                <a
                  href="#resources"
                  className={`nav-link dropdown-toggle ${location.pathname === '/talk-to-sales' || location.pathname.startsWith('/resources') ? 'active' : ''}`}
                  onClick={(e) => {
                    e.preventDefault()
                    setResourcesDropdownOpen(!resourcesDropdownOpen)
                  }}
                >
                  Resources
                  <span className="dropdown-arrow">▼</span>
                </a>
                <div className={`dropdown-menu ${resourcesDropdownOpen ? 'show' : ''}`}>
                  <a href="https://helpdesk-new.celaenotechnology.com/knowledgebase.php" className="dropdown-item">Knowledge Base</a>
                  <a href="https://helpdesk-new.celaenotechnology.com/index.php" className="dropdown-item">Support</a>
                  <Link
                    to="/talk-to-sales"
                    className={`dropdown-item ${location.pathname === '/talk-to-sales' ? 'active' : ''}`}
                  >
                    Talk to Sales
                  </Link>
                </div>
              </div>
              <Link
                to="/pricing"
                className={`nav-link ${location.pathname === '/pricing' ? 'active' : ''}`}
              >
                Pricing
              </Link>
            </nav>
            <div className="action-buttons">
              <a href="/talk-to-sales" className="btn btn-contact">Talk to Sales</a>
              <Link to="/login" className="btn btn-login">Login</Link>
              <Link to="/signup" className="btn btn-signup">Sign up</Link>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}

export default Header
