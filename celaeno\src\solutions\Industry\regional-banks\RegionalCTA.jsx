import { useNavigate } from 'react-router-dom'
import '../tech-saas/TechPerformanceCTA.css'

function CreditCTA() {
  const navigate = useNavigate()

  const handleStartTrialClick = () => {
    navigate('/signup')
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }, 100)
  }

  const handleBookDemoClick = () => {
    navigate('/talk-to-sales')
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }, 100)
  }

  return (
    <section className="tech-performance-cta">
      <div className="tech-performance-cta-container">
        <div className="tech-performance-cta-content">
          <h2 className="tech-performance-cta-title">Grow Together With Better Team Insights</h2>
          
          <div className="tech-performance-cta-buttons">
            <button 
              className="start-trial-btn-1"
              onClick={handleStartTrialClick}
            >
              <span className="btn-main-text">Start Free Trial</span>
              <span className="btn-sub-text">Set up your teams in minutes</span>
            </button>
            <button 
              className="book-demo-btn-1"
              onClick={handleBookDemoClick}
            >
              <span className="btn-main-text">Book a Demo</span>
              <span className="btn-sub-text">Let us tailor Celaeno to your locations and goals</span>
            </button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default CreditCTA
