import './HeroSection.css'
import homeImage from '../assets/home.jpg'
import { useNavigate } from 'react-router-dom'

function HeroSection() {
  const navigate = useNavigate()

  const handleGetStartedClick = () => {
    navigate('/pricing')
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }, 100)
  }
  return (
    <>
      {/* Hero Section */}
      <section className="hero-section">
        <div className="hero-container">
          <div className="hero-content">
            <div className="hero-text">
              <h1 className="hero-title">
                Empower Growth<br />
                Elevate <span className="highlight">Performance</span><br />
                One Team at a Time
              </h1>
              <p className="hero-description">
                Modern performance management software built for small teams and large enterprises alike goal-driven, feedback-first, and ready to scale with you.
              </p>
              <button className="get-started-btn-1" onClick={handleGetStartedClick}>Get Started</button>
            </div>
            <div className="hero-image-container">
              <img
                src={homeImage}
                alt="Celaeno Performance Management Platform"
                className="hero-side-image"
              />
            </div>
          </div>
          <div className="hero-media">
            <div className="media-placeholder">
              {/* Video/Image placeholder */}
            </div>
          </div>
        </div>
        <div className="geometric-bg"></div>
      </section>

      {/* Performance Management Section */}
      <section className="performance-section">
        <div className="performance-container">
          <h2 className="performance-title">What is Celaeno Performance Management?</h2>
          <p className="performance-description">
            Celaeno Performance Management is a comprehensive, cloud-based platform that empowers organizations to build high-performing teams through smart, people-first tools. Designed for flexibility, scalability, and ease-of-use, it's the perfect solution for startups, scaleups, and global enterprises alike. Whether you're looking to streamline performance reviews or providing real-time feedback, Celaeno helps you ditch spreadsheets and outdated HR processes with a seamless digital experience — all backed by AI-powered insights and real-time engagement analytics.
          </p>
        </div>
      </section>
    </>
  )
}

export default HeroSection
