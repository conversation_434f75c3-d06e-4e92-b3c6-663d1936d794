/* Development Page Styles */
.development-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Ensure proper spacing and layout */
.development-page > * {
  flex-shrink: 0;
}

/* Page-level circles */
.page-circles {
  position: absolute;
  top: 100px;
  left: 200px;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 9999;
}

.page-circles .circle-1 {
  position: absolute;
  width: 290px;
  height: 350px;
  background: #007474;
  border-radius: 50%;
  right: 7%;
  top: 65vh;
  z-index: 9999;
  transform: rotate(-26deg);
}

.page-circles .circle-2 {
  position: absolute;
  width: 280px;
  height: 350px;
  background: #4fa6a4;
  border-radius: 50%;
  right: 5%;
  top: 70vh;
  z-index: 9999;
  transform: rotate(-34deg);
}