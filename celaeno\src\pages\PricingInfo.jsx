import './PricingInfo.css'

function PricingInfo() {
  return (
    <section className="pricing-info-section">
      <div className="pricing-info-container">
        <h2 className="pricing-info-title">Not Sure Which Plan Fits?</h2>
        
        <div className="pricing-info-cards">
          {/* Compare Plans Card */}
          <div className="pricing-info-card">
            <p className="pricing-info-text">Compare plans side-by-side</p>
          </div>

          {/* Free Trial Card */}
          <div className="pricing-info-card">
            <p className="pricing-info-text">Try any plan free for 14 days — no credit card required</p>
          </div>

          {/* Discounts Card */}
          <div className="pricing-info-card">
            <p className="pricing-info-text">Discounts available for annual billing and non-profits</p>
          </div>
        </div>

        {/* Add-Ons Section */}
        <div className="add-ons-section">
          <h3 className="add-ons-title">Add-Ons</h3>

          <div className="add-ons-cards">
            {/* First Add-On Card */}
            <div className="add-on-card orange">
              <h4 className="integration-title-1">AI-Powered Feedback Analysis</h4>
              <p className="integration-price-1">$2/user/mo</p>
            </div>

            {/* Second Add-On Card */}
            <div className="add-on-card teal">
              <h4 className="integration-title-2">Advanced Reporting Suite</h4>
              <p className="integration-price-2">$3/user/mo</p>
            </div>
          </div>

          {/* Integration Pack Card */}
          <div className="integration-pack-card">
            <h4 className="integration-title-3">Integration Pack (Jira, Salesforce, etc.)</h4>
            <p className="integration-price-3">$2/user/mo</p>
          </div>
        </div>

        {/* Custom Section */}
        <div className="custom-section">
          <h3 className="custom-title">Need Something Custom?</h3>
          <p className="custom-description">
            Have complex workflows, multi-region teams, or compliance needs?
          </p>
          <button className="custom-demo-btn">Book a Custom Demo</button>
        </div>
      </div>
    </section>
  )
}

export default PricingInfo
