import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { 
  faUsers, 
  faCalendarAlt, 
  faHistory, 
  faTasks, 
  faComments, 
  faRobot 
} from '@fortawesome/free-solid-svg-icons'
import { useNavigate } from 'react-router-dom'
import '../one-on-one/OneOnOneFeatures.css'

function GoalsFeatures() {
  const navigate = useNavigate()

  const handleGetStartedClick = () => {
    navigate('/signup')
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }, 100)
  }

  const features = [
    {
      icon: faUsers,
      title: "OKRs & SMART Goals",
      description: "Choose your framework - whether it's OKRs, SMART goals, or custom setups."
    },
    {
      icon: faCalendarAlt,
      title: "Goal Cascading & Alignment",
      description: "Link team and individual goals to company-wide objectives for total transparency."
    },
    {
      icon: faHistory,
      title: "Progess Tracking",
      description: "Update milestones, add achievements, and track progress with visual indicators."
    },
    {
      icon: faTasks,
      title: "Goal Reviews",
      description: "Set review cadences and auto - reminders to discuss progress in 1:1s or check-ins."
    },
    {
      icon: faComments,
      title: "Goal Visibility Control",
      description: "Choose public, private, or team- only visibility for each goal."
    },
    {
      icon: faRobot,
      title: "Goal Templates by Role/Team",
      description: "Get started faster with templates curated for Sales, HR, Marketing, and more."
    }
  ]

  return (
    <section className="one-on-one-features">
      <div className="one-on-one-features-container">
        <h2 className="one-on-one-features-title">What You Can Do with Celaeno Goals</h2>
        <div className="one-on-one-features-grid">
          {features.map((feature, index) => (
            <div key={index} className="one-on-one-feature-card">
              <div className="one-on-one-feature-icon">
                <FontAwesomeIcon icon={feature.icon} />
              </div>
              <h3 className="one-on-one-feature-title">{feature.title}</h3>
              <p className="one-on-one-feature-description">{feature.description}</p>
            </div>
          ))}
          <div className="one-on-one-cta-container">
            <button className="one-on-one-cta-btn" onClick={handleGetStartedClick}>Get Started</button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default GoalsFeatures
