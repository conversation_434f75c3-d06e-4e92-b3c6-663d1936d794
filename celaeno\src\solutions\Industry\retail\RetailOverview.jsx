import '../tech-saas/TechSaasOverview.css'

function RetailOverview() {
  return (
    <section className="tech-saas-overview">
      <div className="tech-saas-overview-container">
        <div className="tech-saas-overview-content">
          <h2 className="tech-saas-overview-title">
            Built for Multi-Location, Multi-Role Retail Operations
          </h2>
          <p className="tech-saas-overview-description">
            From part-time associates to regional managers, Celaeno scales across roles, shifts, and store locations. Align goals, capture feedback, and simplify reviews — without slowing down the sales floor.
          </p>
        </div>
      </div>
    </section>
  )
}

export default RetailOverview
