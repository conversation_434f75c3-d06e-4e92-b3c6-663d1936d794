/* One-on-One Process Section */
.one-on-one-process {
  background: white;
  padding: 6rem 0;
  position: relative;
}

.one-on-one-process-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
}

.one-on-one-process-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #007474;
  margin-bottom: 3rem;
  line-height: 1.2;
}

.process-curve-container {
  position: relative;
  width: 100%;
  height: 300px;
  margin-left: -6rem;
  margin-bottom: 10rem;
}

.process-curve-svg {
  width: 100%;
  height: 100%;
}

.process-steps {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
}

.process-step {
  text-align: center;
  width: 280px;
  position: relative;
}

.process-step:first-child {
  margin-top: -260px;
  text-align: center;
  
}

.process-step:nth-child(2) {
  margin-top: -265px;
  margin-left: -50px;
  text-align: center;
}

.process-step:last-child {
  text-align: center;
  margin-top: -295px;
  margin-right: 50px;
}

.process-step-title {
  font-size: 1.4rem;
  font-weight: 700;
  color: #007474;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.process-step-description {
  font-size: 1rem;
  line-height: 1.6;
  color: #555;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 968px) {
  .one-on-one-process {
    padding: 4rem 0;
  }

  .one-on-one-process-title {
    font-size: 2.2rem;
    margin-bottom: 2.5rem;
  }

  .process-curve-container {
    height: 250px;
  }

  .process-steps {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .process-step:first-child,
  .process-step:last-child {
    text-align: center;
  }

  .process-step-title {
    font-size: 1.3rem;
  }

  .process-step-description {
    font-size: 0.95rem !important;
  }
}

@media (max-width: 768px) {
  .one-on-one-process {
    padding: 3rem 0;
  }

  .one-on-one-process-title {
    font-size: 2rem;
    margin-bottom: 2rem;
  }

  .process-curve-container {
    height: 200px;
    margin-bottom: 1.5rem;
  }

  .process-steps {
    gap: 2.5rem;
  }

  .process-step-title {
    font-size: 1.2rem;
    margin-bottom: 0.8rem;
  }

  .process-step-description {
    font-size: 0.9rem !important;
  }
}

@media (max-width: 480px) {
  .one-on-one-process-title {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
  }

  .one-on-one-process-container {
    padding: 0 1rem;
  }

  .process-curve-container {
    height: 150px;
  }

  .process-steps {
    gap: 2rem;
  }

  .process-step-title {
    font-size: 1.1rem;
  }

  .process-step-description {
    font-size: 0.85rem !important;
  }
}


/* Benefits Overlay - Positioned between process and footer */
.benefits-overlay {
  position: absolute;
  bottom: -9rem;
  left: 0;
  right: 0;
  z-index: 10;
}

.benefits-overlay-container {
  max-width: 1200px;
    margin-bottom: 30px;
    margin-left: auto;
    margin-right: auto;
  padding: 0 2rem;
}

.benefits-overlay-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #007474;
  margin-bottom: 2.5rem;
  line-height: 1.2;
}

.benefits-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.benefit-card {
  background: white;
  padding: 2.5rem 2rem;
  border-radius: 1rem;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #e9ecef;
}

.benefit-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.benefit-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: #007474;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.benefit-description {
  font-size: 1rem;
  line-height: 1.6;
  color: #555;
  margin: 0;
}

/* Responsive Design for Benefits */
@media (max-width: 968px) {
  .benefits-overlay {
    bottom: -6rem;
  }

  .benefits-overlay-title {
    font-size: 2.2rem;
    margin-bottom: 1.5rem;
  }

  .benefits-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  .benefit-card {
    padding: 2rem 1.5rem;
  }

  .benefit-title {
    font-size: 1.2rem;
  }

  .benefit-description {
    font-size: 0.95rem;
  }
}

@media (max-width: 768px) {
  .benefits-overlay {
    bottom: -5rem;
  }

  .benefits-overlay-title {
    font-size: 2rem;
    margin-bottom: 1.5rem;
  }

  .benefits-cards {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .benefit-card {
    padding: 2rem 1.5rem;
  }

  .benefit-title {
    font-size: 1.1rem;
    margin-bottom: 0.8rem;
  }

  .benefit-description {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .benefits-overlay-title {
    font-size: 1.8rem;
    margin-bottom: 1rem;
  }

  .benefits-overlay-container {
    padding: 0 1rem;
  }

  .benefits-cards {
    gap: 1rem;
  }

  .benefit-card {
    padding: 1.5rem 1rem;
  }

  .benefit-title {
    font-size: 1rem;
  }

  .benefit-description {
    font-size: 0.85rem;
  }
}
