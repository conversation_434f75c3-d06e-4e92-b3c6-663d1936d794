import Header from '../components/Header'
import Footer from '../components/Footer'
import './TalkToSales.css'

function TalkToSales() {
  return (
    <div className="talk-to-sales-page">
      <Header />

      {/* Hero Section */}
      <section className="talk-to-sales-hero">
        <div className="talk-to-sales-hero-container">
          <h1 className="talk-to-sales-title">Talk to Our Sales Team</h1>
          <p className="talk-to-sales-subtitle">
            Share your details and we'll be in touch shortly. Our team is here to guide you through pricing, product fit, and anything else you need.
          </p>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="talk-to-sales-form-section">
        <div className="talk-to-sales-form-container">
          <div className="form-content">
            <div className="form-left-content">
              <div className="info-card">
                <h3 className="info-card-title">Why Talk to Sales?</h3>
                <ul className="info-list">
                  <li className="info-item">Get personalized guidance from product experts</li>
                  <li className="info-item">Learn how our solution can scale with your team</li>
                  <li className="info-item">Fast response — usually within 1 business day</li>
                  <li className="info-item">Security and compliance you can rely on</li>
                </ul>
              </div>

              <div className="contact-card">
                <h3 className="contact-card-title">Or reach us directly</h3>
                <div className="contact-info">
                  <p className="contact-email">Email: <EMAIL></p>

                </div>
              </div>
            </div>

            <div className="form-right-content">
              <form className="talk-to-sales-form">
                <div className="form-row">
                  <div className="form-group">
                    <input
                      type="text"
                      id="fullName"
                      name="fullName"
                      className="form-input"
                      placeholder="Full Name (required)"
                      required
                    />
                  </div>
                  <div className="form-group">
                    <input
                      type="email"
                      id="email"
                      name="email"
                      className="form-input"
                      placeholder="Business Email (required)"
                      required
                    />
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <input
                      type="text"
                      id="company"
                      name="company"
                      className="form-input"
                      placeholder="Company Name (required)"
                      required
                    />
                  </div>
                  <div className="form-group">
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      className="form-input"
                      placeholder="Phone Number"
                    />
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <select id="role" name="role" className="form-select">
                      <option value="">Role</option>
                      <option value="ceo">CEO/Founder</option>
                      <option value="hr-manager">HR Manager</option>
                      <option value="team-lead">Team Lead</option>
                      <option value="manager">Manager</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                  <div className="form-group">
                    <select id="companySize" name="companySize" className="form-select">
                      <option value="">Company Size</option>
                      <option value="1-10">1-10 employees</option>
                      <option value="11-50">11-50 employees</option>
                      <option value="51-200">51-200 employees</option>
                      <option value="201-500">201-500 employees</option>
                      <option value="500+">500+ employees</option>
                    </select>
                  </div>
                </div>

                <div className="form-group full-width">
                  <textarea
                    id="message"
                    name="message"
                    className="form-textarea"
                    placeholder="Message / What do you want to discuss?"
                    rows="4"
                  ></textarea>
                </div>

                <div className="form-footer">
                  <button type="submit" className="form-submit-btn">Request a Call</button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}

export default TalkToSales
