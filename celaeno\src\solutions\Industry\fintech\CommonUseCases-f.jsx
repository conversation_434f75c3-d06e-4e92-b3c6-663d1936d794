import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faBullseye, faSync, faCompass, faComments } from '@fortawesome/free-solid-svg-icons'
import '../tech-saas/CommonUseCases.css'

function CommonUseCases_f() {
  const useCases = [
    {
      icon: faBullseye,
      title: "Align Product, Risk, and GTM Teams on Shared Goals",
      description: "Create transparency across engineering, fraud, and business development."
    },
    {
      icon: faSync,
      title: "Track Regulatory & Security Readiness via Performance Metrics",
      description: "Map team goals and reviews to key compliance objectives (KYC, AML, PCI-DSS)."
    },
    {
      icon: faCompass,
      title: "Use OKRs to Drive Scale & Clarity",
      description: "Connect business milestones with measurable team objectives."
    },
    {
      icon: faComments,
      title: "Run Manager-Led 1:1s with Career Focus",
      description: "Support team development in fast-evolving environments."
    }
  ]

  return (
    <section className="common-use-cases">
      <div className="common-use-cases-container">
        <h2 className="common-use-cases-title">Common Use Cases</h2>
        
        <div className="use-cases-grid-1">
          {useCases.map((useCase, index) => (
            <div key={index} className="use-case-item">
              <div className="use-case-icon">
                <FontAwesomeIcon icon={useCase.icon} />
              </div>
              <div className="use-case-content">
                <h3 className="use-case-title">{useCase.title}</h3>
                <p className="use-case-description-1">{useCase.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default CommonUseCases_f
