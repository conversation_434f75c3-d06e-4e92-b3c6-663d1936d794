import { useNavigate } from 'react-router-dom'
import '../tech-saas/TechPerformanceCTA.css'

function ManufacturingCTA() {
  const navigate = useNavigate()

  const handleStartTrialClick = () => {
    navigate('/signup')
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }, 100)
  }

  const handleBookDemoClick = () => {
    navigate('/talk-to-sales')
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }, 100)
  }

  return (
    <section className="tech-performance-cta">
      <div className="tech-performance-cta-container">
        <div className="tech-performance-cta-content">
          <h2 className="tech-performance-cta-title">Performance That Builds Better Output</h2>
          
          <div className="tech-performance-cta-buttons">
            <button 
              className="start-trial-btn-1"
              onClick={handleStartTrialClick}
            >
              <span className="btn-main-text">Start Free Trial</span>
              
            </button>
            <button 
              className="book-demo-btn-1"
              onClick={handleBookDemoClick}
            >
              <span className="btn-main-text">Talk to Sales </span>
              
            </button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default ManufacturingCTA
