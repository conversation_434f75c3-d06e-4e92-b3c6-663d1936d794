import '../../../features/one-on-one/OneOnOneHero.css'
import { useNavigate } from 'react-router-dom'

function RetailHero() {
  const navigate = useNavigate()

  const handleGetStartedClick = () => {
    navigate('/pricing')
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }, 100)
  }

  return (
    <section className="one-on-one-hero">
      <div className="one-on-one-hero-container">
        <div className="one-on-one-hero-content">
          <h1 className="one-on-one-hero-title">
            Empower Teams-Improve Service <br />
             Drive Sales


          </h1>
          <p className="one-on-one-hero-description">
            Your retail workforce is your brand. Celaeno helps retail businesses build stronger store teams, <br />
             improve engagement, and drive results — with mobile-friendly tools built for speed, simplicity, and scale.
          </p>
          <button className="one-on-one-hero-btn" onClick={handleGetStartedClick}>Get Started</button>
        </div>
      </div>
      
      {/* Curved Bottom */}
      <div className="one-on-one-hero-curve">
        <svg viewBox="0 0 1200 200" preserveAspectRatio="none">
          <path d="M0,0 Q600,200 1200,0 L1200,200 L0,200 Z" fill="white"/>
        </svg>
      </div>
    </section>
  )
}

export default RetailHero
