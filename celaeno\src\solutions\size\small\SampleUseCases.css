.sample-use-cases {
  padding: 4rem 0;
  background-color: white;
  position: relative;
}

.sample-use-cases-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.sample-use-cases-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #007474;
  text-align: center;
  margin-bottom: 3rem;
  line-height: 1.2;
}

.use-cases-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1000px;
  margin: 0 auto;
}

.use-case-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #e5e7eb;
  position: relative;
  overflow: hidden;
}

.use-case-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.use-case-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.use-case-content {
  text-align: center;
}

.use-case-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #007474;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.use-case-description {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sample-use-cases {
    padding: 3rem 0;
  }
  
  .sample-use-cases-container {
    padding: 0 1rem;
  }
  
  .sample-use-cases-title {
    font-size: 2rem;
    margin-bottom: 2rem;
  }
  
  .use-cases-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .use-case-card {
    padding: 1.5rem;
  }
  
  .use-case-title {
    font-size: 1.3rem;
    margin-bottom: 0.8rem;
  }
  
  .use-case-description {
    font-size: 0.95rem;
  }
}

@media (max-width: 480px) {
  .use-case-card {
    padding: 1.2rem;
  }
  
  .use-case-title {
    font-size: 1.2rem;
  }
  
  .use-case-description {
    font-size: 0.9rem;
  }
}
