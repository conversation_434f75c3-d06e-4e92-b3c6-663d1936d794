/* Talk to Sales Page */
.talk-to-sales-page {
  background-color: white;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Hero Section */
.talk-to-sales-hero {
  background-color: #007474;
  padding: 6rem 0;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 70%;
  margin: 0 auto;
}

.talk-to-sales-hero-container {
  max-width: 1200px;
  text-align: center;
  color: white;
}

.talk-to-sales-title {
  font-size: 3rem;
  font-weight: 700;
  margin: 0 0 2rem 0;
  line-height: 1.2;
}

.talk-to-sales-subtitle {
  font-size: 1.3rem;
  line-height: 1.6;
  margin: 0;
  opacity: 0.95;
}

/* Responsive Design */
@media (max-width: 768px) {
  .talk-to-sales-hero {
    padding: 4rem 0;
  }

  .talk-to-sales-hero-container {
    width: 85%;
    padding: 0 1rem;
  }

  .talk-to-sales-title {
    font-size: 2.2rem;
    margin-bottom: 1.5rem;
  }

  .talk-to-sales-subtitle {
    font-size: 1.1rem;
  }
}

/* Contact Form Section */
.talk-to-sales-form-section {
  background-color: white;
  padding: 6rem 0;
  display: flex;
  justify-content: center;
}

.talk-to-sales-form-container {
  width: 90%;
  max-width: 1200px;
}

.form-content {
  display: flex;
  gap: 4rem;
  align-items: flex-start;
}

.form-left-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.info-card,
.contact-card {
  background-color: white;
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.info-card-title {
  color: #007474;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 1.5rem 0;
}

.info-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.info-item {
  font-weight: 600;
  color: #333;
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 1rem;
  position: relative;
  padding-left: 1.5rem;
}

.info-item:before {
  content: "●";
  color: #ffab68;
  font-size: 1.2rem;
  position: absolute;
  left: 0;
  top: -4px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.contact-card-title {
  color: #ffab68;
  font-size: 1.3rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
}

.contact-info {
  margin: 0;
}

.contact-email,
.contact-phone {
  color: #007474;
  font-size: 1rem;
  font-weight: 500;
  margin: 0.5rem 0;
  line-height: 1.5;
}

.form-right-content {
  flex: 1;
}

.talk-to-sales-form {
  margin-top: -160px;
  background-color: #f1f1ec;
  padding: 2.5rem;
  border-radius: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.form-group {
  flex: 1;
}

.form-group.full-width {
  width: 100%;
  margin-bottom: 1.5rem;
}

.form-input,
.form-select,
.form-textarea,
.form-textarea-small {
  width: 100%;
  padding: 1rem;
  border: 2px solid #007474;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-family: inherit;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
  background-color: white;
  color: #000;
}

.form-input::placeholder,
.form-textarea::placeholder,
.form-textarea-small::placeholder {
  color: #666;
  font-weight: 500;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus,
.form-textarea-small:focus {
  outline: none;
  border-color: #005555;
}

.form-select {
  cursor: pointer;
  color: #666;
  font-weight: 500;
}

.form-select option {
  color: #333;
  font-weight: 500;
}

.form-select option:first-child {
  font-weight: 500;
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.form-textarea-small {
  resize: vertical;
  min-height: 80px;
}

.form-footer {
  margin-top: 2rem;
}

.required-text {
  color: #e74c3c;
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.form-submit-btn {
  background-color: #ffab68;
  color: #333;
  border: none;
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.form-submit-btn:hover {
  background-color: #ff9a4d;
}

/* Form Responsive Design */
@media (max-width: 1024px) {
  .form-content {
    gap: 2rem;
  }

  .talk-to-sales-form {
    padding: 2rem;
  }
}

@media (max-width: 768px) {
  .talk-to-sales-form-section {
    padding: 4rem 0;
  }

  .talk-to-sales-form-container {
    width: 95%;
    padding: 0 1rem;
  }

  .form-content {
    flex-direction: column;
    gap: 2rem;
  }

  .form-row {
    flex-direction: column;
    gap: 1rem;
  }

  .talk-to-sales-form {
    padding: 1.5rem;
  }

  .info-card,
  .contact-card {
    padding: 1.5rem;
  }

  .info-card-title {
    font-size: 1.3rem;
  }

  .contact-card-title {
    font-size: 1.1rem;
  }

  .form-input,
  .form-select,
  .form-textarea,
  .form-textarea-small {
    padding: 0.875rem;
  }

  .form-submit-btn {
    padding: 1rem 2rem;
    font-size: 1rem;
  }
}
