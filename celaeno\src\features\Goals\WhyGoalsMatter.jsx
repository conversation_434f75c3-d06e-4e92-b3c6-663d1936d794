import '../one-on-one/WhyOneOnOnesMatter.css'

function WhyGoalsMatter() {
  return (
    <section className="why-one-on-ones-matter">
      <div className='circle-1-g'></div>
      <div className='circle-2-g'></div>
      <div className="why-one-on-ones-container">
        <div className="why-one-on-ones-content">
          <h2 className="why-one-on-ones-title">Why Goals Matters</h2>
          <p className="why-one-on-ones-description">
            Goals aren't just metrics — they're momentum. <PERSON><PERSON><PERSON> helps you set, 
            share, track, and crush your objectives with clarity and confidence. 
            Whether you're samll startup or a global team, everyone can see how their work 
            connects to the bigger picture.

          </p>
          <p className="why-one-on-ones-subtitle">
            No more scattered docs, confusing KPIs, or goals lost in spreadsheets. 
          </p>
        </div>
      </div>
    </section>
  )
}

export default WhyGoalsMatter
