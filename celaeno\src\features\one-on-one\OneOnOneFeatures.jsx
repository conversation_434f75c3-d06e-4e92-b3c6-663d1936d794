import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faUsers,
  faCalendarAlt,
  faHistory,
  faTasks,
  faComments,
  faRobot
} from '@fortawesome/free-solid-svg-icons'
import { useNavigate } from 'react-router-dom'
import './OneOnOneFeatures.css'

function OneOnOneFeatures() {
  const navigate = useNavigate()

  const handleGetStartedClick = () => {
    navigate('/signup')
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }, 100)
  }

  const features = [
    {
      icon: faUsers,
      title: "Collaborative Agendas",
      description: "Employees and managers co-create agenda items ahead of time to stay focused and aligned."
    },
    {
      icon: faCalendarAlt,
      title: "Smart Scheduling",
      description: "Sync with Google Calendar, Outlook, or Teams and never miss a beat."
    },
    {
      icon: faHistory,
      title: "Conversation History",
      description: "All past discussions are saved and searchable — so you never lose context."
    },
    {
      icon: faTasks,
      title: "Action Items & Follow-Ups",
      description: "Assign next steps during the meeting and track progress automatically."
    },
    {
      icon: faComments,
      title: "Feedback Integration",
      description: "Surface real-time feedback and recent achievements right within the meeting view."
    },
    {
      icon: faRobot,
      title: "AI-Generated Prompts",
      description: "Not sure what to start? AI Assistant suggests talking points based on role, recent activity, and goals."
    }
  ]

  return (
    <section className="one-on-one-features">
      <div className="one-on-one-features-container">
        <h2 className="one-on-one-features-title">What you Can Do with Celaeno Performance 1:1 Meetings</h2>
        <div className="one-on-one-features-grid">
          {features.map((feature, index) => (
            <div key={index} className="one-on-one-feature-card">
              <div className="one-on-one-feature-icon">
                <FontAwesomeIcon icon={feature.icon} />
              </div>
              <h3 className="one-on-one-feature-title">{feature.title}</h3>
              <p className="one-on-one-feature-description">{feature.description}</p>
            </div>
          ))}
          <div className="one-on-one-cta-container">
            <button className="one-on-one-cta-btn" onClick={handleGetStartedClick}>Get Started</button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default OneOnOneFeatures
