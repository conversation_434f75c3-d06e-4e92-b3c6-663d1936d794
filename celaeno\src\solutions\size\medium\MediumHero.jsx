import '../../../features/one-on-one/OneOnOneHero.css'
import { useNavigate } from 'react-router-dom'

function MediumHero() {
  const navigate = useNavigate()

  const handleGetStartedClick = () => {
    navigate('/pricing')
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }, 100)
  }

  return (
    <section className="one-on-one-hero">
      <div className="one-on-one-hero-container">
        <div className="one-on-one-hero-content">
          <h1 className="one-on-one-hero-title">
            Structure Meets Flexibility <br />
            Built to Grow With You 
            


          </h1>
          <p className="one-on-one-hero-description">
            Your teams are expanding, your departments are evolving, and people performance is becoming<br />
            mission-critical. Celaeno gives medium businesses the structure of enterprise-grade tools with the <br /> flexibility growing organizations need to thrive.
          </p>
          <button className="one-on-one-hero-btn" onClick={handleGetStartedClick}>Get Started</button>
        </div>
      </div>
      
      {/* Curved Bottom */}
      <div className="one-on-one-hero-curve">
        <svg viewBox="0 0 1200 200" preserveAspectRatio="none">
          <path d="M0,0 Q600,200 1200,0 L1200,200 L0,200 Z" fill="white"/>
        </svg>
      </div>
    </section>
  )
}

export default MediumHero
