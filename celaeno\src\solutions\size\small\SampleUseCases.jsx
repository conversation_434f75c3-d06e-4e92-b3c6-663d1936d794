import './SampleUseCases.css'

function SampleUseCases() {
  const useCases = [
    {
      title: "Startups",
      description: "Track OKRs and keep hybrid teams aligned from Series A to C"
    },
    {
      title: "Agencies",
      description: "Give creatives and project managers clear review paths"
    },
    {
      title: "Boutique Retail or Local Services",
      description: "Run reviews and give praise — without expensive HR overhead"
    }
  ]

  return (
    <section className="sample-use-cases">
      <div className="sample-use-cases-container">
        <h2 className="sample-use-cases-title">Sample Use Cases</h2>
        
        <div className="use-cases-grid">
          {useCases.map((useCase, index) => (
            <div key={index} className="use-case-card">
              <div className="use-case-content">
                <h3 className="use-case-title">{useCase.title}</h3>
                <p className="use-case-description">{useCase.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default SampleUseCases
