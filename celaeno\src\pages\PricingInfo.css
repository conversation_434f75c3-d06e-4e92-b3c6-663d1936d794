.pricing-info-section {
  padding: 4rem 0 0 0;
  background-color: white;
  position: relative;
  z-index: 2;
}

.pricing-info-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.pricing-info-title {
  color: #007474;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 3rem;
  text-align: center;
}

.pricing-info-cards {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
  width: 100%;
}

.pricing-info-card {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  flex: 1;
  min-width: 250px;
  max-width: 350px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pricing-info-text {
  color: #007474;
  font-weight: 600;
  font-size: 1.1rem;
  text-align: center;
  margin: 0;
}

/* Add-Ons Section */
.add-ons-section {
  margin-top: 4rem;
  width: 100%;
}

.add-ons-title {
  color: #007474;
  font-size: 2rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 2rem;
}

.add-ons-cards {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.add-on-card {
  border-radius: 1rem;
  width: 300px;
  height: 200px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.add-on-card.orange {
  background: #ffab68;
  width: 450px;
  padding: 2rem;
}

.add-on-card.teal {
  background: #4fa6a4;
  width: 450px;
  padding: 2rem;
}

.integration-pack-card {
  background: #e8e8e8;
  border-radius: 1rem;
  padding: 2rem;
  text-align: left;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  width: 900px;
  margin: 0 auto;
}

.integration-title-1 {
  color: #333;
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.integration-price-1 {
  color: white;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}

.integration-title-2 {
  color: #333;
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.integration-price-2 {
  color: white;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}

.integration-title-3 {
  color: #333;
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.integration-price-3 {
  color: #007474;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}

/* Custom Section */
.custom-section {
  background: #007474;
  padding: 3rem 2rem;
  text-align: center;
  margin-top: 3rem;
  width: 99vw;
}

.custom-title {
  color: white;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.custom-description {
  color: white;
  font-size: 1.3rem;
  margin-bottom: 2rem;
  line-height: 1.5;
}

.custom-demo-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid white;
  padding: 0.875rem 2rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.custom-demo-btn:hover {
  background: white;
  color: #4fa6a4;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .pricing-info-container {
    padding: 0 2rem;
  }
}

@media (max-width: 768px) {
  .pricing-info-section {
    padding: 3rem 0;
  }

  .pricing-info-container {
    padding: 0 1rem;
  }

  .pricing-info-cards {
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
  }

  .pricing-info-card {
    width: 100%;
    max-width: 350px;
    padding: 1.5rem;
  }

  .add-ons-cards {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .add-on-card {
    width: 100%;
    max-width: 300px;
    padding: 1.5rem;
  }

  .custom-section {
    margin-top: 2rem;
    padding: 2rem 1.5rem;
  }

  .custom-title {
    font-size: 1.8rem;
    margin-bottom: 1rem;
  }

  .custom-description {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }

  .custom-demo-btn {
    padding: 0.8rem 1.8rem;
    font-size: 1rem;
    width: 100%;
    max-width: 280px;
  }
}

@media (max-width: 480px) {
  .pricing-info-section {
    padding: 2rem 0;
  }

  .pricing-info-container {
    padding: 0 0.5rem;
  }

  .pricing-info-card {
    max-width: 100%;
    padding: 1.2rem;
  }

  .add-on-card {
    max-width: 100%;
    padding: 1.2rem;
  }

  .custom-section {
    padding: 1.5rem 1rem;
  }

  .custom-title {
    font-size: 1.5rem;
  }

  .custom-description {
    font-size: 0.9rem;
  }

  .custom-demo-btn {
    padding: 0.7rem 1.5rem;
    font-size: 0.9rem;
  }
}
