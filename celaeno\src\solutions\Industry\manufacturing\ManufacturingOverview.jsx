import '../tech-saas/TechSaasOverview.css'

function ManufacturingOverview() {
  return (
    <section className="tech-saas-overview">
      <div className="tech-saas-overview-container">
        <div className="tech-saas-overview-content">
          <h2 className="tech-saas-overview-title">
            Built for the Shop Floor and Beyond
          </h2>
          <p className="tech-saas-overview-description">
            Whether you're running a single plant or global production operations, Celaeno adapts to rotating shifts, unionized roles, safety metrics, and upskilling needs. Drive continuous improvement — without disrupting the line.
          </p>
        </div>
      </div>
    </section>
  )
}

export default ManufacturingOverview
