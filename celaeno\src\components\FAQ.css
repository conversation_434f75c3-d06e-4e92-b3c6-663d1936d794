/* FAQ Section */
.faq-section {
  background-color: white;
  padding: 6rem 0;
}

.faq-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.faq-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #007474;
  text-align: center;
  margin: 0 0 4rem 0;
  line-height: 1.2;
}

/* FAQ List */
.faq-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.faq-item {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}

.faq-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

/* FAQ Question Button */
.faq-question {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  text-align: left;
  outline: none;
}

.faq-question:focus {
  outline: none;
}

.faq-question-text {
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
  line-height: 1.4;
  flex: 1;
  padding-right: 1rem;
}

/* FAQ Icon */
.faq-icon {
  font-size: 1.5rem;
  font-weight: 300;
  color: #6b7280;
  transition: transform 0.2s ease;
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.faq-icon.open {
  transform: rotate(45deg);
}

/* FAQ Answer */
.faq-answer {
  margin-top: 1rem;
  padding-right: 2rem;
}

.faq-answer p {
  font-size: 1rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
}

/* Hover Effects */
.faq-question:hover .faq-question-text {
  color: #1f2937;
}

.faq-question:hover .faq-icon {
  color: #4fa6a4;
}

/* Responsive Design */
@media (max-width: 768px) {
  .faq-section {
    padding: 4rem 0;
  }
  
  .faq-title {
    font-size: 2rem;
    margin-bottom: 3rem;
  }
  
  .faq-question-text {
    font-size: 1rem;
  }
  
  .faq-answer {
    padding-right: 1rem;
  }
  
  .faq-answer p {
    font-size: 0.95rem;
  }
}

@media (max-width: 480px) {
  .faq-container {
    padding: 0 1rem;
  }
  
  .faq-title {
    font-size: 1.75rem;
  }
  
  .faq-question-text {
    font-size: 0.95rem;
    padding-right: 0.5rem;
  }
  
  .faq-icon {
    font-size: 1.25rem;
  }
  
  .faq-answer {
    padding-right: 0.5rem;
  }
}
