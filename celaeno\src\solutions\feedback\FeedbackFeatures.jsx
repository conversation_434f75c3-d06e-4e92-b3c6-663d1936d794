import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { 
  faUsers, 
  faCalendarAlt, 
  faHistory, 
  faTasks, 
  faComments, 
  faRobot 
} from '@fortawesome/free-solid-svg-icons'
import { useNavigate } from 'react-router-dom'
import './FeedbackFeatures.css'

function FeedbackFeatures() {
  const navigate = useNavigate()

  const handleGetStartedClick = () => {
    navigate('/signup')
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }, 100)
  }
  const features = [
    {
      icon: faUsers,
      title: "Real-Time Feedback",
      description: "Send instant praise or suggestions directly from Slack, Teams, or Celaeno."
    },
    {
      icon: faCalendarAlt,
      title: "Private or Public Options",
      description: "Keep feedback between individuals or celebrate wins across the company feed."
    },
    {
      icon: faHistory,
      title: "Role-Based Feedback Templates",
      description: "Pre-built feedback prompts tailored by department or role to make giving feedback easier and more impactful."
    },
    {
      icon: faTasks,
      title: "360° Feedback Support",
      description: "Enable multi-rater feedback from peers, managers, direct reports, and even external collaborators."
    },
    {
      icon: faComments,
      title: "Feedback Tagging",
      description: "Categorize feedback by skill, value, or goal alignment to track developmental themes."
    },
    {
      icon: faRobot,
      title: "AI-Enhanced Coaching Tips",
      description: "Get smart suggestions on how to word your feedback constructively and effectively."
    }
  ]

  return (
    <section className="one-on-one-features">
      <div className="one-on-one-features-container">
        <h2 className="one-on-one-features-title">Key Features</h2>
        <div className="one-on-one-features-grid">
          {features.map((feature, index) => (
            <div key={index} className="one-on-one-feature-card">
              <div className="one-on-one-feature-icon">
                <FontAwesomeIcon icon={feature.icon} />
              </div>
              <h3 className="one-on-one-feature-title">{feature.title}</h3>
              <p className="one-on-one-feature-description">{feature.description}</p>
            </div>
          ))}
          <div className="one-on-one-cta-container">
            <button className="one-on-one-cta-btn" onClick={handleGetStartedClick}>Get Started</button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default FeedbackFeatures
