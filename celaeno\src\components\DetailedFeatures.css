/* Detailed Features Section */
.detailed-features-section {
  background-color: white;
  padding: 6rem 0;
  position: relative;
}

/* Gradient effect on right side - */
.detailed-features-section::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 100%;
  background: linear-gradient(to right, transparent, rgba(255, 107, 53, 0.15));
  pointer-events: none;
  z-index: 1;
}

/* Header Section */
.detailed-features-header {
  text-align: left;
  margin-bottom: 4rem;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding: 0 2rem;
}

.detailed-features-main-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #007474;
  margin: 0 0 1rem 0;
  line-height: 1.2;
}

.detailed-features-subtitle {
  font-size: 1.2rem;
  color: #000000;
  font-weight: 700;
  margin: 0;
  line-height: 1.5;
}

.detailed-features-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.detailed-features-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 4rem 3rem;
  align-items: start;
}

/* Detailed Feature Cards */
.detailed-feature-card {
  text-align: left;
  display: flex;
  flex-direction: column;
}

.detailed-feature-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
  margin-bottom: 1.5rem;
}

.detailed-feature-title {
  font-size: 2rem;
  font-weight: 700;
  color: #007474;
  line-height: 1.2;
  margin: 0 0 1.5rem 0;
}

.detailed-feature-description {
  font-size: 1.1rem;
  color: #374151;
  line-height: 1.6;
  margin: 0 0 2rem 0;
  flex-grow: 1;
}

.detailed-feature-link {
  color: #007474;
  font-size: 1rem;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.2s ease;
  margin-top: auto;
  text-align: right;
  display: block;
}

.detailed-feature-link:hover {
  color: #005555;
  text-decoration: underline;
}

/* Centered CTA Section */
.detailed-features-cta {
  display: flex;
  justify-content: center;
  margin-top: 3rem;
}

.get-started-btn-detailed {
  background-color: #007474;
  color: white;
  border: none;
  padding: 0.875rem 2rem;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.get-started-btn-detailed:hover {
  background-color: #005555;
}


/* Responsive Design */
@media (max-width: 1024px) {
  .detailed-features-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 3rem 2rem;
  }
}

@media (max-width: 768px) {
  .detailed-features-section {
    padding: 4rem 0;
  }

  .detailed-features-header {
    margin-bottom: 3rem;
    padding: 0 1rem;
  }

  .detailed-features-main-title {
    font-size: 2rem;
  }

  .detailed-features-subtitle {
    font-size: 1.1rem;
  }

  .detailed-features-grid {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .detailed-feature-icon {
    width: 50px;
    height: 50px;
    margin-bottom: 1rem;
  }

  .detailed-feature-title {
    font-size: 1.5rem;
  }

  .detailed-feature-description {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .detailed-features-main-title {
    font-size: 1.75rem;
  }

  .detailed-features-subtitle {
    font-size: 1rem;
  }

  .detailed-features-container {
    padding: 0 1rem;
  }

  .detailed-feature-icon {
    width: 45px;
    height: 45px;
  }

  .detailed-feature-title {
    font-size: 1.25rem;
  }

  .detailed-feature-description {
    font-size: 0.95rem;
  }
}
