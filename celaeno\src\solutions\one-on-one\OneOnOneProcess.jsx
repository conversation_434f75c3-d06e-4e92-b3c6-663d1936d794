import './OneOnOneProcess.css'

function OneOnOneProcess() {
  const processSteps = [
    {
      title: "Set the Cadence",
      description: "Choose weekly, bi-weekly, or custom meeting frequencies. Celaeno sends auto-reminders to both participants."
    },
    {
      title: "Build the Agenda Together",
      description: "Drag and drop talking points or add new ones on the fly. Attach goals, feedback, or notes for deeper discussion."
    },
    {
      title: "Track Progress & Outcomes",
      description: "Mark action items, capture decisions, and view trends over time across teams or individuals."
    }
  ]

  return (
    <section className="one-on-one-process">
      <div className="one-on-one-process-container">
        <h2 className="one-on-one-process-title">How It Works (3-Step Flow with Visual Placeholder Suggestions)</h2>
        {/* SVG with curved line and positioned dots */}
        <div className="process-curve-container">
          <svg viewBox="0 0 1000 300" className="process-curve-svg">
            {/* Curved line */}
            <path
              d="M 0 200 Q 300 80 500 150 Q 700 220 950 120 Q 1000 100 1100 110"
              stroke="#333"
              strokeWidth="2"
              fill="none"
            />

            {/* Dots positioned on the curve */}
            <circle cx="150" cy="150" r="12" fill="#007474" />
            <circle cx="500" cy="150" r="12" fill="#007474" />
            <circle cx="900" cy="140" r="12" fill="#007474" />
          </svg>
        </div>

        {/* Text content positioned below dots */}
        <div className="process-steps">
          {processSteps.map((step, index) => (
            <div key={index} className="process-step">
              <h3 className="process-step-title">{step.title}</h3>
              <p className="process-step-description">{step.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Benefits Cards - Positioned to span between process and footer */}
      <div className="benefits-overlay">
        <div className="benefits-overlay-container">
          <div>
            <h2 className="benefits-overlay-title">Designed for Everyone</h2>
          </div>
          <div className="benefits-cards">
            <div className="benefit-card">
              <h3 className="benefit-title">Employees:</h3>
              <p className="benefit-description">Get recognized and coached in real time</p>
            </div>
            <div className="benefit-card">
              <h3 className="benefit-title">Managers:</h3>
              <p className="benefit-description">Give better, more consistent input with less friction</p>
            </div>
            <div className="benefit-card">
              <h3 className="benefit-title">HR Teams:</h3>
              <p className="benefit-description">Roll out organization-wide feedback initiatives with ease</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default OneOnOneProcess
