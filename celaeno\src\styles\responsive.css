/* Global Responsive Utilities */

/* Ensure all images are responsive */
img {
  max-width: 100%;
  height: auto;
}

/* Prevent horizontal scrolling */
html, body {
  overflow-x: hidden;
  width: 100%;
}

/* Container responsive utilities */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (max-width: 1024px) {
  .container {
    padding: 0 2rem;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 0.5rem;
  }
}

/* Typography responsive utilities */
.responsive-title {
  font-size: clamp(1.8rem, 4vw, 3.5rem);
  line-height: 1.2;
}

.responsive-subtitle {
  font-size: clamp(1rem, 2.5vw, 1.5rem);
  line-height: 1.4;
}

.responsive-text {
  font-size: clamp(0.9rem, 2vw, 1.1rem);
  line-height: 1.6;
}

/* Button responsive utilities */
.responsive-btn {
  padding: clamp(0.7rem, 2vw, 1rem) clamp(1.2rem, 3vw, 2rem);
  font-size: clamp(0.9rem, 2vw, 1.1rem);
  border-radius: 8px;
  transition: all 0.3s ease;
}

/* Grid responsive utilities */
.responsive-grid-2 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.responsive-grid-3 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.responsive-grid-4 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1.5rem;
}

@media (max-width: 768px) {
  .responsive-grid-2,
  .responsive-grid-3,
  .responsive-grid-4 {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* Flex responsive utilities */
.responsive-flex {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
}

@media (max-width: 768px) {
  .responsive-flex {
    flex-direction: column;
    align-items: stretch;
  }
}

/* Spacing responsive utilities */
.responsive-section {
  padding: clamp(2rem, 5vw, 4rem) 0;
}

.responsive-margin-bottom {
  margin-bottom: clamp(1rem, 3vw, 2rem);
}

/* Card responsive utilities */
.responsive-card {
  padding: clamp(1rem, 3vw, 2rem);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.responsive-card:hover {
  transform: translateY(-5px);
}

/* Table responsive utilities */
.responsive-table {
  width: 100%;
  overflow-x: auto;
}

.responsive-table table {
  min-width: 600px;
}

@media (max-width: 768px) {
  .responsive-table table {
    min-width: 100%;
  }
}

/* Hide/Show utilities for different screen sizes */
.hide-mobile {
  display: block;
}

.show-mobile {
  display: none;
}

@media (max-width: 768px) {
  .hide-mobile {
    display: none;
  }
  
  .show-mobile {
    display: block;
  }
}

.hide-tablet {
  display: block;
}

.show-tablet {
  display: none;
}

@media (max-width: 1024px) and (min-width: 769px) {
  .hide-tablet {
    display: none;
  }
  
  .show-tablet {
    display: block;
  }
}

/* Touch-friendly utilities */
@media (max-width: 768px) {
  .touch-friendly {
    min-height: 44px;
    min-width: 44px;
  }
  
  .touch-friendly-padding {
    padding: 0.8rem 1.2rem;
  }
}

/* Prevent text overflow */
.text-responsive {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* Responsive video/iframe */
.responsive-video {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.responsive-video iframe,
.responsive-video video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Form responsive utilities */
.responsive-form {
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

.responsive-form input,
.responsive-form textarea,
.responsive-form select {
  width: 100%;
  padding: 0.8rem 1rem;
  font-size: 1rem;
  border-radius: 6px;
  border: 1px solid #ddd;
  margin-bottom: 1rem;
}

@media (max-width: 480px) {
  .responsive-form input,
  .responsive-form textarea,
  .responsive-form select {
    padding: 0.7rem 0.8rem;
    font-size: 0.9rem;
  }
}
