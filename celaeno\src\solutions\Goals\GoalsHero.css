/* One-on-One Hero Section */
.one-on-one-hero {
  background: linear-gradient(140deg, #50a6a5 0%, white 100%);
  padding: 6rem 0 8rem 0;
  position: relative;
  overflow: hidden;
}

.one-on-one-hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 60px 2rem;
}

.one-on-one-hero-content {
  max-width: 600px;
}

.one-on-one-hero-title {
  font-size: 3rem;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 2rem;
  color: #333;
  white-space: nowrap;
}

.one-on-one-hero-description {
  font-size: 1.2rem;
  line-height: 1.6;
  margin-bottom: 3rem;
  color: #555;
  max-width: 500px;
  white-space: nowrap;
  font-weight: 600;
}

.one-on-one-hero-btn {
  background-color: #007474;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.one-on-one-hero-btn:hover {
  background-color: #007474;
}

/* Curved Bottom */
.one-on-one-hero-curve {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.one-on-one-hero-curve svg {
  width: 100%;
  height: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
  .one-on-one-hero {
    padding: 4rem 0 6rem 0;
    text-align: center;
  }

  .one-on-one-hero-content {
    max-width: 100%;
  }

  .one-on-one-hero-title {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
  }

  .one-on-one-hero-description {
    font-size: 1.1rem;
    margin-bottom: 2.5rem;
    max-width: 100%;
  }

  .one-on-one-hero-curve {
    height: 150px;
  }
}

@media (max-width: 480px) {
  .one-on-one-hero {
    padding: 3rem 0 5rem 0;
  }

  .one-on-one-hero-title {
    font-size: 2rem;
  }

  .one-on-one-hero-description {
    font-size: 1rem;
  }

  .one-on-one-hero-btn {
    width: 100%;
    max-width: 300px;
  }

  .one-on-one-hero-curve {
    height: 100px;
  }
}
