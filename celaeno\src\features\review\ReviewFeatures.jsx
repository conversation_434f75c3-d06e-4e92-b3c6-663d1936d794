import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { 
  faUsers, 
  faCalendarAlt, 
  faHistory, 
  faTasks, 
  faComments, 
  faRobot 
} from '@fortawesome/free-solid-svg-icons'
import { useNavigate } from 'react-router-dom'
import '../one-on-one/OneOnOneFeatures.css'


function ReviewFeatures() {
  const navigate = useNavigate()

  const handleGetStartedClick = () => {
    navigate('/signup')
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }, 100)
  }

  const features = [
    {
      icon: faUsers,
      title: "Fully Customizable Review Cycles",
      description: "Annual, bi-annual, project-based, or ad-hoc build what fits your org's rhythm."
    },
    {
      icon: faCalendarAlt,
      title: "360° Feedback",
      description: "Get a complete picture from peers, managers, direct reports, and self-reflections."
    },
    {
      icon: faHistory,
      title: "Bias Reduction Tools",
      description: "Al-powered flagging for vague language, extremes, or potential bias."
    },
    {
      icon: faTasks,
      title: "Goal & Feedback Integration",
      description: "Tie review ratings directly to tracked goals and recent feedback conversations."
    },
    {
      icon: faComments,
      title: "Shared or Private Results",
      description: "Managers and HR can control visibility settings to foster transparency or discretion."
    },
    {
      icon: faRobot,
      title: "Calibrated Ratings",
      description: "Use moderation dashboards to ensure fairness across teams and departments."
    }
  ]

  return (
    <section className="one-on-one-features">
      <div className="one-on-one-features-container">
        <h2 className="one-on-one-features-title">Review Features That Work for You</h2>
        <div className="one-on-one-features-grid">
          {features.map((feature, index) => (
            <div key={index} className="one-on-one-feature-card">
              <div className="one-on-one-feature-icon">
                <FontAwesomeIcon icon={feature.icon} />
              </div>
              <h3 className="one-on-one-feature-title">{feature.title}</h3>
              <p className="one-on-one-feature-description">{feature.description}</p>
            </div>
          ))}
          <div className="one-on-one-cta-container">
            <button className="one-on-one-cta-btn" onClick={handleGetStartedClick}>Get Started</button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default ReviewFeatures
