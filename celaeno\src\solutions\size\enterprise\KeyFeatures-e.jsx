import '../small/KeyFeatures.css'

function KeyFeatures_e() {
  const features = [
    {
      feature: "Multi-BU Review Frameworks",
      benefit: "Customize review cycles by division, region, or job function"
    },
    {
      feature: "Global Goal Alignment Tools",
      benefit: "Create top-down or cascading OKRs with role targeting"
    },
    {
      feature: "Advanced Compliance Support",
      benefit: "SOC2, GDPR, HIPAA-ready, ISO 27001 aware"
    },
    {
      feature: "API, SDKs, and Developer Tools",
      benefit: "Extend Celaeno into your ecosystem"
    },
    {
      feature: "Talent Bench Matrix & 9-Box Grid",
      benefit: "For succession planning and promotion mapping"
    }
  ]

  return (
    <section className="key-features">
      <div className='gradient-right'>
        
      </div>
      <div className="key-features-container">
        <h2 className="key-features-title">Key Features for Large Organizations</h2>
        
        <div className="features-table">
          <div className="table-header">
            <div className="header-feature">Feature</div>
            <div className="header-benefit">Enterprise Advantage</div>
          </div>
          
          <div className="table-body">
            {features.map((item, index) => (
              <div key={index} className="table-row">
                <div className="feature-cell">{item.feature}</div>
                <div className="benefit-cell">{item.benefit}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default KeyFeatures_e
