import '../../../features/one-on-one/OneOnOneHero.css'
import { useNavigate } from 'react-router-dom'

function CreditUnionHero() {
  const navigate = useNavigate()

  const handleGetStartedClick = () => {
    navigate('/pricing')
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }, 100)
  }

  return (
    <section className="one-on-one-hero">
      <div className="one-on-one-hero-container">
        <div className="one-on-one-hero-content">
          <h1 className="one-on-one-hero-title">
            Empower People-Serve Communities <br /> 
            Elevate Member Impact


          </h1>
          <p className="one-on-one-hero-description">
            Credit unions run on mission, trust, and community. Celaeno’s performance platform helps you <br />
            develop your people, uphold compliance, and align every branch and team member to your <br />
            member-first mission — with tools built for the way credit unions operate.
          </p>
          <button className="one-on-one-hero-btn" onClick={handleGetStartedClick}>Get Started</button>
        </div>
      </div>
      
      {/* Curved Bottom */}
      <div className="one-on-one-hero-curve">
        <svg viewBox="0 0 1200 200" preserveAspectRatio="none">
          <path d="M0,0 Q600,200 1200,0 L1200,200 L0,200 Z" fill="white"/>
        </svg>
      </div>
    </section>
  )
}

export default CreditUnionHero
