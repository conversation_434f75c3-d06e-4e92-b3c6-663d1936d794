import '../one-on-one/OneOnOneProcess.css'


function ReviewProcess() {
  const processSteps = [
    {
      title: "Kick Off the Review Cycle",
      description: "Choose participants, format, and timeline - and notify everyone in one click."
    },
    {
      title: "Gather Input from All Angles",
      description: "Self-assessments, manager reviews, and peer feedback collected into a clean view."
    },
    {
      title: "Align on Ratings & Next Steps",
      description: "Managers review feedback, assign ratings, and set development plans for growth."
    }
  ]

  return (
    <section className="one-on-one-process">
      <div className="one-on-one-process-container">
        <h2 className="one-on-one-process-title">The Review Flow (Snapshot) </h2>
        {/* SVG with curved line and positioned dots */}
        <div className="process-curve-container">
          <svg viewBox="0 0 1000 300" className="process-curve-svg">
            {/* Curved line */}
            <path
              d="M 0 200 Q 300 80 500 150 Q 700 220 950 120 Q 1000 100 1100 110"
              stroke="#333"
              strokeWidth="2"
              fill="none"
            />

            {/* Dots positioned on the curve */}
            <circle cx="150" cy="150" r="12" fill="#007474" />
            <circle cx="500" cy="150" r="12" fill="#007474" />
            <circle cx="900" cy="140" r="12" fill="#007474" />
          </svg>
        </div>

        {/* Text content positioned below dots */}
        <div className="process-steps">
          {processSteps.map((step, index) => (
            <div key={index} className="process-step">
              <h3 className="process-step-title">{step.title}</h3>
              <p className="process-step-description">{step.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Benefits Cards - Positioned to span between process and footer */}
      <div className="benefits-overlay">
        <div className="benefits-overlay-container">
          <div>
            <h2 className="benefits-overlay-title">Tailored for Every Team Size</h2>
          </div>
          <div className="benefits-cards">
            <div className="benefit-card">
              <h3 className="benefit-title">Startups:</h3>
              <p className="benefit-description">Light, fast reviews that scale as you grow</p>
            </div>
            <div className="benefit-card">
              <h3 className="benefit-title">SMBs:</h3>
              <p className="benefit-description">Fair, transparent processes that boost morale</p>
            </div>
            <div className="benefit-card">
              <h3 className="benefit-title">Enterprises:</h3>
              <p className="benefit-description">Structured, compliant reviews with deep analytics</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default ReviewProcess
