/* Key Capabilities Section */
.key-capabilities {
  background-color: #f1f1ec;
  padding: 5rem 0;
}

.key-capabilities-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.key-capabilities-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #007474;
  text-align: left;
  margin-bottom: 4rem;
  line-height: 1.2;
}

.capabilities-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 4rem 3rem;
  align-items: start;
}

.capability-item {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.capability-icon {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}



.capability-icon svg {
  font-size: 3rem;
  color: #ff6b35;
  transition: all 0.3s ease;
}

.capability-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #007474;
  margin-bottom: 1rem;
  line-height: 1.3;
  text-align: center;
}

.capability-description {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  text-align: center;
  max-width: 280px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .capabilities-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 3rem 2rem;
  }
}

@media (max-width: 768px) {
  .key-capabilities {
    padding: 4rem 0;
  }

  .key-capabilities-title {
    font-size: 2rem;
    margin-bottom: 3rem;
    text-align: center;
  }

  .capabilities-grid {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .capability-item {
    max-width: 350px;
    margin: 0 auto;
  }
}

@media (max-width: 480px) {
  .key-capabilities {
    padding: 3rem 0;
  }

  .key-capabilities-title {
    font-size: 1.8rem;
    margin-bottom: 2.5rem;
  }

  .capabilities-grid {
    gap: 2.5rem;
  }

  .capability-icon {
    width: 70px;
    height: 70px;
    margin-bottom: 1.25rem;
  }

  .capability-icon svg {
    font-size: 2.5rem;
  }

  .capability-title {
    font-size: 1.2rem;
  }

  .capability-description {
    font-size: 0.95rem;
  }
}
