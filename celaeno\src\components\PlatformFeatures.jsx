import './PlatformFeatures.css'
import { useNavigate } from 'react-router-dom'
function PlatformFeatures() {
  const navigate = useNavigate()

  const handleExploreClick = (route) => {
    navigate(route)
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }, 100)
  }

  const features = [
    {
      title: "1:1 Meetings",
      description: "Run meaningful check-ins with shared agendas, action items, and progress tracking.",
      image: "/api/placeholder/300/200",
      route: "/solutions/one-on-one"
    },
    {
      title: "Feedback",
      description: "Empower real-time recognition and constructive input with continuous feedback loops.",
      image: "/api/placeholder/300/200",
      route: "/solutions/feedback"
    },
    {
      title: "Goals & OKRs",
      description: "Align teams to company vision using flexible, transparent goal-setting frameworks.",
      image: "/api/placeholder/300/200",
      route: "/solutions/goals"
    },
    {
      title: "Performance Reviews",
      description: "Run fair, customizable, and insightful reviews at scale.",
      image: "/api/placeholder/300/200",
      route: "/solutions/review"
    },
    {
      title: "Personal Development",
      description: "Support career growth with role paths, skills tracking, and mentorship programs.",
      image: "/api/placeholder/300/200",
      route: "/solutions/development"
    }
  ]

  return (
    <section className="platform-features">
      <div className='gradient-circle-left'>

      </div>
      <div className='gradient-circle-right'>

      </div>
      <div className="platform-features-container">
        <div className="platform-features-grid">
          {features.map((feature, index) => (
            <div key={index} className="platform-feature-card">
              <div className="platform-feature-image">
                <div className="platform-feature-placeholder"></div>
              </div>
              <div className="platform-feature-content">
                <h3 className="platform-feature-title">{feature.title}</h3>
                <p className="platform-feature-description">{feature.description}</p>
                <button
                  className="platform-feature-explore"
                  onClick={() => handleExploreClick(feature.route)}
                >
                  Explore <span className="explore-arrow">→</span>
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default PlatformFeatures
