import '../../size/small/keyFeatures.css'

function KeyFeatures_ma() {
  const features = [
    {
      feature: "Shift-Based Goal Templates",
      benefit: "Align expectations by shift, team, or role"
    },
    {
      feature: "Supervisor Feedback Toolkits",
      benefit: "Support real-time performance coaching"
    },
    {
      feature: "Skills Matrix & Training Tracker",
      benefit: "Identify cross-training needs and skill gaps"
    },
    {
      feature: "Compliance Checklist Reviews",
      benefit: "Evaluate safety adherence and ISO standards"
    },
    {
      feature: "Mobile or Kiosk Feedback Options",
      benefit: "Capture data on the floor or in break rooms"
    }
  ]

  return (
    <section className="key-features">
      <div className='gradient-right'>
        
      </div>
      <div className="key-features-container">
        <h2 className="key-features-title">Key Features for Credit Unions</h2>
        
        <div className="features-table">
          <div className="table-header">
            <div className="header-feature">Feature</div>
            <div className="header-benefit">Why It Works for Credit Unions</div>
          </div>
          
          <div className="table-body">
            {features.map((item, index) => (
              <div key={index} className="table-row">
                <div className="feature-cell">{item.feature}</div>
                <div className="benefit-cell">{item.benefit}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default KeyFeatures_ma
