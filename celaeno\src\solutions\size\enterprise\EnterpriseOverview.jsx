import '../../Industry/tech-saas/TechSaasOverview.css'
import './EnterpriseOverview.css'

function EnterpriseOverview() {
  return (
    <section className="tech-saas-overview">
      <div className='circle-1-enterprise'></div>
      <div className='circle-2-enterprise'></div>
      <div className="tech-saas-overview-container">
        <div className="tech-saas-overview-content">
          <h2 className="tech-saas-overview-title">
            Enterprise-Grade Performance at Every Layer
          </h2>
          <p className="tech-saas-overview-description">
            You have multiple business units, layers of management, geographic regions, and compliance to worry about. Celaeno was built with all of this in mind — offering the flexibility your managers want, with the control HR and leadership teams need.
          </p>
        </div>
      </div>
    </section>
  )
}

export default EnterpriseOverview
