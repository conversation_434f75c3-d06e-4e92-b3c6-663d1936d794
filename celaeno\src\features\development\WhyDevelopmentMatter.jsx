import '../one-on-one/WhyOneOnOnesMatter.css'

function WhyDevelopmentMatter() {
  return (
    <section className="why-one-on-ones-matter">
      <div className='circle-1'></div>
      <div className='circle-2'></div>
      <div className="why-one-on-ones-container">
        <div className="why-one-on-ones-content">
          <h2 className="why-one-on-ones-title">Why Personal Development Matters</h2>
          <p className="why-one-on-ones-description">
            Performance management without growth is just paperwork. 
            That's why Celaeno helps you go beyond reviews and build personalized, actionable, and measurable development plans 
            tied to real skills and goals.

          </p>
          <p className="why-one-on-ones-subtitle">
            Whether someone wants to lead a team, switch roles, or master 
            a new technology - give them the framework to grow with purpose.
          </p>
        </div>
      </div>
    </section>
  )
}

export default WhyDevelopmentMatter
