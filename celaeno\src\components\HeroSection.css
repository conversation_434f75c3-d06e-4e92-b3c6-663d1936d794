/* Hero Section */
.hero-section {
  position: relative;
  background-color: white;
  min-height: 80vh;
  overflow: hidden;
  margin-bottom: 0;
  padding-bottom: 0;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 4rem 2rem;
  position: relative;
  z-index: 2;
}

.hero-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 4rem;
  margin-bottom: 3rem;
  padding-bottom: 150px;
}

.hero-text {
  flex: 1;
  max-width: 500px;
  position: relative;
  z-index: 2;
  text-align: left;
}

.hero-image-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 2;
}

.hero-side-image {
  max-width: 139%;
  margin-left: 150px;
  height: auto;

}


.hero-title {
  font-size: 3rem;
  font-weight: 700;
  line-height: 1.1;
  color: #1f2937;
  margin-bottom: 1.5rem;
}

.highlight {
  color: #007474;
}

.hero-description {
  font-size: 1.125rem;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.get-started-btn-1 {
  background-color: #007474;
  color: white;
  border: none;
  padding: 0.875rem 2rem;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.get-started-btn:hover {
  background-color: #005555;
}

.hero-media {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 1000px;
  margin: 4rem auto 0 auto;
  padding: 0 2rem;
  margin-bottom: 0;
}

.media-placeholder {
  width: 100%;
  height: 350px;
  background-color: #d1d5db;
  border-radius: 1rem;
  border: none;
  outline: none;
  box-shadow: none;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  font-size: 1.125rem;
  position: relative;
  z-index: 2;
}

/* Geometric Background */
.geometric-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #007474;
  clip-path: polygon(0 39%, 100% 75%, 100% 100%, 0% 100%);

  z-index: 1;
}

/* Performance Management Section */
.performance-section {
  background-color: #007474;
  color: white;
  padding: 4rem 0;
  margin-top: 0;
}

.performance-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.performance-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 2rem;
  color: white;
}

.performance-description {
  font-size: 1.125rem;
  line-height: 1.7;
  color: rgba(255, 255, 255, 0.9);
  max-width: 900px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-container {
    padding: 0 2rem;
  }

  .hero-title {
    font-size: 3rem;
  }

  .hero-description {
    font-size: 1.1rem;
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding: 2rem 0;
  }

  .hero-container {
    padding: 0 1rem;
  }

  .hero-content {
    flex-direction: column;
    gap: 2rem;
    margin-bottom: 2rem;
    align-items: center;
    text-align: center;
  }

  .hero-text {
    text-align: center;
    max-width: 100%;
  }

  .hero-image-container {
    order: -1;
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .hero-side-image {
    max-width: 280px;
    width: 100%;
    height: auto;
  }

  .hero-title {
    font-size: 2.2rem;
    line-height: 1.2;
    margin-bottom: 1rem;
  }

  .hero-description {
    font-size: 1rem;
    line-height: 1.5;
    margin-bottom: 1.5rem;
  }

  .hero-buttons {
    flex-direction: column;
    gap: 1rem;
    align-items: center;
    width: 100%;
  }

  .hero-buttons .btn {
    width: 100%;
    max-width: 280px;
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
  }

  .hero-media {
    margin: 2rem auto 0 auto;
    padding: 0 1rem;
  }

  .media-placeholder {
    height: 200px;
  }

  .geometric-bg {
    clip-path: polygon(0 70%, 100% 50%, 100% 100%, 0% 100%);
  }

  .performance-title {
    font-size: 1.6rem;
    line-height: 1.3;
  }

  .performance-description {
    font-size: 0.95rem;
    line-height: 1.5;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 1.5rem 0;
  }

  .hero-container {
    padding: 0 0.5rem;
  }

  .hero-title {
    font-size: 1.8rem;
    line-height: 1.2;
  }

  .hero-description {
    font-size: 0.9rem;
  }

  .hero-side-image {
    max-width: 250px;
  }

  .hero-buttons .btn {
    padding: 0.7rem 1.2rem;
    font-size: 0.9rem;
  }

  .media-placeholder {
    height: 180px;
  }

  .performance-title {
    font-size: 1.4rem;
  }

  .performance-description {
    font-size: 0.85rem;
  }
}
