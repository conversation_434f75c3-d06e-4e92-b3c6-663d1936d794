import '../small/WhyChooseCelaeno.css'

function WhyChooseCelaeno_m() {
  return (
    <section className="why-choose-celaeno">
      <div className='gradient-left'>
        
      </div>
      <div className="why-choose-container">
        <div className="why-choose-content">
          <h2 className="why-choose-title">Why Medium Businesses Choose Celaeno</h2>
          
        
          
          <div className="why-choose-features">
            <div className="feature-item">
              <h3>Performance reviews that flex with each department</h3>
            </div>
            <div className="feature-item">
              <h3>Dashboards to support strategic people decisions</h3>
            </div>
            <div className="feature-item">
              <h3>Templates to standardize while staying agile</h3>
            </div>
            <div className="feature-item">
              <h3>Toolkits for managers & visibility for HR</h3>
            </div>
          </div>
          
          <p className="why-choose-conclusion">
            Whether you’re scaling tech teams, managing remote departments, or preparing for the next round of 
            funding — Celaeno is your strategic people platform.
          </p>
        </div>
      </div>
    </section>
  )
}

export default WhyChooseCelaeno_m
