import Header from '../../components/Header'
import Footer from '../../components/Footer'
import OneOnOneHero from './OneOnOneHero'
import WhyOneOnOnesMatter from './WhyOneOnOnesMatter'
import OneOnOneFeatures from './OneOnOneFeatures'
import OneOnOneProcess from './OneOnOneProcess'
import './OneOnOnePage.css'

function OneOnOnePage() {
  return (
    <div className="one-on-one-page">
      <Header />
      <OneOnOneHero />

      {/* Circles positioned at page level */}
      <div className="page-circles">
        <div className="circle-1"></div>
        <div className="circle-2"></div>
      </div>

      <WhyOneOnOnesMatter />
      <OneOnOneFeatures />
      <OneOnOneProcess />

      {/* Additional content sections will be added here */}
      <Footer />
    </div>
  )
}

export default OneOnOnePage
