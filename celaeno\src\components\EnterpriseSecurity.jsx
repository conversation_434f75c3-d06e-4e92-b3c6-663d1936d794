import './EnterpriseSecurity.css'

function EnterpriseSecurity() {
  const securityFeatures = [
    "Role-based access & permissions",
    "Encrypted data and secure APIs",
    "GDPR & SOC2 compliance",
    "Custom audit logs and approval flows"
  ]

  return (
    <section className="enterprise-security">
      <div className="enterprise-security-container">
        <h2 className="enterprise-security-title">Enterprise-Grade Security</h2>
        
        <div className="security-features-grid">
          {securityFeatures.map((feature, index) => (
            <div key={index} className="security-feature-item">
              <span className="security-bullet">•</span>
              <span className="security-text">{feature}</span>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default EnterpriseSecurity
