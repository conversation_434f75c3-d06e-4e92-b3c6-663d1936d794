/* Feedback Page Styles */
.feedback-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Ensure proper spacing and layout */
.feedback-page > * {
  flex-shrink: 0;
}

/* Page-level circles */
.page-circles {
  position: absolute;
  top: 100px;
  left: 200px;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 9999;
}

.page-circles .circle-1 {
  position: absolute;
  width: 290px;
  height: 350px;
}

.page-circles .circle-2 {
  position: absolute;
  width: 280px;
  height: 350px;
}
