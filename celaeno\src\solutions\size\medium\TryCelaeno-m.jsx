import { useNavigate } from 'react-router-dom'
import '../small/TryCelaeno.css'

function TryCelaeno_m() {
  const navigate = useNavigate()

  const handleStartTrialClick = () => {
    navigate('/signup')
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }, 100)
  }

  const handleTalkToSpecialistClick = () => {
    navigate('/talk-to-sales')
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }, 100)
  }

  return (
    <section className="try-celaeno">
      <div className="try-celaeno-container">
        <div className="try-celaeno-content">
          <h2 className="try-celaeno-title">Build Your Culture as You Scale</h2>
          
          <div className="try-celaeno-buttons">
            <button 
              className="start-trial-btn"
              onClick={handleStartTrialClick}
            >
              Start Free Trial
            </button>
            <button 
              className="talk-specialist-btn"
              onClick={handleTalkToSpecialistClick}
            >
              Talk to Sales About Enterprise Transition
            </button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default TryCelaeno_m
