import { useState } from 'react'
import './FilterSection.css'

function FilterSection() {
  const [activeFilter, setActiveFilter] = useState('company-size')

  return (
    <section className="filter-section">
      <div className="filter-header">
        <h2 className="filter-main-title">Solutions Tailored to You</h2>
        <p className="filter-subtitle">
          Because no two teams are the same, Celaeno offers precision-fit<br />
          solutions for every business model, team size, and industry.
        </p>
      </div>

      <div className="filter-container">
        <div className="filter-content">
          <div className="filter-left">
            <div className="filter-buttons">
              <button
                className={`filter-btn ${activeFilter === 'industry' ? 'active' : ''}`}
                onClick={() => setActiveFilter('industry')}
              >
                By Industry:
              </button>
              <button
                className={`filter-btn ${activeFilter === 'company-size' ? 'active' : ''}`}
                onClick={() => setActiveFilter('company-size')}
              >
                By Company Size:
              </button>
            </div>
          </div>

          <div className="filter-right">
            <div className="filter-points">
              {activeFilter === 'company-size' ? (
                <>
                  <div className="filter-point">
                    <div className="filter-point-bullet">•</div>
                    <div className="filter-point-content">
                      <h4 className="filter-point-title">Small Businesses (1-50 employees)</h4>
                      <p className="filter-point-description">
                        Get started fast with ready-made templates, intuitive<br />
                        dashboards, and an AI assistant that helps you every step of the way.
                      </p>
                    </div>
                  </div>

                  <div className="filter-point">
                    <div className="filter-point-bullet">•</div>
                    <div className="filter-point-content">
                      <h4 className="filter-point-title">Medium-Sized Companies (51-500 employees)</h4>
                      <p className="filter-point-description">
                        Scale with confidence using collaborative OKRs, team-level<br />
                        analytics, and manager training workflows to grow your leaders.
                      </p>
                    </div>
                  </div>

                  <div className="filter-point">
                    <div className="filter-point-bullet">•</div>
                    <div className="filter-point-content">
                      <h4 className="filter-point-title">Enterprises (500+ employees)</h4>
                      <p className="filter-point-description">
                        Unlock advanced role management, organization-wide goal<br />
                        cascading, custom review cycles, and enterprise integrations.
                      </p>
                    </div>
                  </div>
                </>
              ) : (
                <>
                  <div className="filter-point">
                    <div className="filter-point-bullet">•</div>
                    <div className="filter-point-content">
                      <h4 className="filter-point-title">Tech & SaaS</h4>
                      <p className="filter-point-description">
                        Agile workflows, growth metrics, and fast feedback loops
                      </p>
                    </div>
                  </div>

                  <div className="filter-point">
                    <div className="filter-point-bullet">•</div>
                    <div className="filter-point-content">
                      <h4 className="filter-point-title">Healthcare</h4>
                      <p className="filter-point-description">
                        Compliance-driven reviews, credential tracking, team wellness
                      </p>
                    </div>
                  </div>

                  <div className="filter-point">
                    <div className="filter-point-bullet">•</div>
                    <div className="filter-point-content">
                      <h4 className="filter-point-title">Education</h4>
                      <p className="filter-point-description">
                        Academic staff goals, continuous development, engagement
                      </p>
                    </div>
                  </div>

                  <div className="filter-point">
                    <div className="filter-point-bullet">•</div>
                    <div className="filter-point-content">
                      <h4 className="filter-point-title">Retail</h4>
                      <p className="filter-point-description">
                        Frontline feedback, seasonal staff reviews, mobile-first tools
                      </p>
                    </div>
                  </div>

                  <div className="filter-point">
                    <div className="filter-point-bullet">•</div>
                    <div className="filter-point-content">
                      <h4 className="filter-point-title">Finance & Legal</h4>
                      <p className="filter-point-description">
                        Role-specific scorecards, high-compliance frameworks
                      </p>
                    </div>
                  </div>

                  <div className="filter-point">
                    <div className="filter-point-bullet">•</div>
                    <div className="filter-point-content">
                      <h4 className="filter-point-title">Manufacturing</h4>
                      <p className="filter-point-description">
                        Supervisor check-ins, productivity benchmarks, team KPIs
                      </p>
                    </div>
                  </div>
                </>
              )}
            </div>

            <div className="filter-link-container">
              <a href="#" className="filter-link">
                {activeFilter === 'company-size'
                  ? 'Find the Right Fit for Your Team →'
                  : 'See Industry Use Cases →'
                }
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default FilterSection
