/* Footer Section */
.footer-section {
  background: linear-gradient(135deg, #b8e6e6 0%, #d4f1f1 100%);
  position: relative;
  overflow: hidden;
}

.footer-content {
  padding: 12rem 0 4rem 0;
  position: relative;
  z-index: 2;
}

.footer-cta {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: left;
}

.footer-title {
  font-size: 3rem;
  font-weight: 700;
  color: #007474;
  margin: 0 0 1.5rem 0;
  line-height: 1.2;
  max-width: 800px;
  white-space: nowrap;
}

.footer-subtitle {
  font-size: 1.2rem;
  color: #ff8c2b;
  margin: 0 0 3rem 0;
  line-height: 1.5;
  font-weight: 500;
}

/* Footer Buttons */
.footer-buttons {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.footer-btn {
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  text-decoration: none;
  display: inline-block;
}

.footer-btn-outline {
  background-color: #007474;
  color: white;
  border: 2px solid #007474;
}

.footer-btn-outline:hover {
  background-color: #007474;
  color: white;
}

.footer-btn-filled {
  background-color: #007474;
  color: white;
  border: 2px solid #007474;
}

.footer-btn-filled:hover {
  background-color: #005a5a;
  border-color: #005a5a;
}

/* Footer Trial Info */
.footer-trial-info {
  margin-top: 1.5rem;
  text-align: left;
}

.footer-trial-text {
  font-size: 15px;
  color: #ff8c2b;
  margin: 0.25rem 0;
  font-weight: 500;

}

/* Curved Section */
.footer-curved-section {
  position: relative;
  height: 400px;
}

.footer-curve {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.footer-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  height: 430px;
  z-index: 2;
  padding: 2rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Gradient Border Box */
.footer-box {
  width: 90%;
  max-width: 1200px;
  height: 350px;
  position: relative;
  background-color: transparent;
}



.footer-logo-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1rem;
  grid-column: 1;
}

.footer-logo-link {
  display: inline-block;
  text-decoration: none;
}

.footer-logo {
  height: 50px;
  width: auto;
  opacity: 0.8;
}

/* Social Media Icons */
.footer-social-icons {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
  margin-left: 40px;
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: #FF6B35;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  transition: all 0.2s ease;
  font-size: 1.1rem;
}


/* Copyright Text */
.footer-copyright {
  color: #FF6B35;
  font-size: 0.9rem;
  font-weight: 500;
  margin-left: 35px;
}

/* Legal Links */
.footer-legal-links {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 8rem;
  margin-left: 35px;
  white-space: nowrap;
  flex-wrap: nowrap;
}

.footer-legal-link {
  color: #007474;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: color 0.2s ease;
}

.footer-legal-link:hover {
  color: #005555;
  text-decoration: underline;
}

.footer-legal-separator {
  color: #666;
  font-size: 0.85rem;
}

/* Right Bottom Copyright */
.footer-right-copyright {
  position: absolute;
  top: 20.75rem;
  right: 2rem;
  color: #007474;
  font-size: 0.9rem;
  font-weight: 500;
  white-space: nowrap;
}

/* Footer Sections Grid */
.footer-box-content {
  width: 100%;
  height: 100%;
  background-color: white;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  gap: 2rem;
  padding: 2rem;
  position: relative;
}

/* Footer Sections */
.footer-solutions {
  color: white;
  grid-column: 2;
}

.Footer-industry {
  color: white;
  grid-column: 3;
}

.footer-company-size {
  color: white;
  grid-column: 4;
}

.footer-resources {
  color: white;
  grid-column: 5;
}


.footer-solutions-title {
  color: #FF8c2b;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  margin-top: 0;
}

.footer-solutions-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.footer-solution-link {
  color:  black;
  text-decoration: none;
  font-size: 0.95rem;
  transition: color 0.2s ease;
}

/* Gradient Border Lines with Curves */
.footer-box {
  border: 4px solid transparent;
  border-bottom: none;
  border-radius: 40px 40px 0 0;
  overflow: visible;
  position: relative;
}

.footer-box::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  height: 4px;
  background: linear-gradient(90deg, #007474 0%, #ff8c2b 100%);
  border-radius: 40px 40px 0 0;
  z-index: 2;
}

.footer-box::after {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  width: 4px;
  height: calc(100vh + 4px);
  background: #007474;
  border-radius: 40px 0 0 0;
  z-index: 1;
}

.footer-box-content::after {
  content: '';
  position: absolute;
  top: -4px;
  right: -4px;
  width: 4px;
  height: calc(100vh + 4px);
  background: #ff8c2b;
  border-radius: 0 40px 0 0;
  z-index: 1;
}

/* Decorative Elements */
.footer-section::after {
  content: '';
  position: absolute;
  bottom: 20%;
  left: -100px;
  width: 200px;
  height: 200px;
  background: linear-gradient(45deg, rgba(0, 116, 116, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  z-index: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .footer-content {
    padding: 7rem 0 3rem 0;
  }
  
  .footer-cta {
    padding: 0 1rem;
  }
  
  .footer-title {
    font-size: 2.2rem;
  }
  
  .footer-subtitle {
    font-size: 1.1rem;
    margin-bottom: 2rem;
  }
  
  .footer-buttons {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }
  
  .footer-btn {
    padding: 0.875rem 1.5rem;
    text-align: center;
  }
  
  .footer-curved-section {
    height: 300px;
  }
  
  .footer-bottom {
    height: 320px;
    padding: 1.5rem;
  }

  .footer-box {
    height: 250px;
  }

  .footer-logo {
    height: 40px;
  }

  .footer-box-content {
    grid-template-columns: 1fr 1fr 1fr;
    gap: 1.5rem;
  }

  .footer-logo-section {
    grid-column: 1;
  }

  .footer-solutions {
    grid-column: 2;
  }

  .Footer-industry {
    grid-column: 3;
  }

  .footer-company-size,
  .footer-resources {
    display: none;
  }

  .social-icon {
    width: 36px;
    height: 36px;
    font-size: 1rem;
  }

  .footer-solutions-title {
    font-size: 1.1rem;
  }

  .footer-solution-link {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .footer-title {
    font-size: 1.8rem;
  }
  
  .footer-subtitle {
    font-size: 1rem;
  }
  
  .footer-btn {
    padding: 0.75rem 1.25rem;
    font-size: 0.95rem;
  }
  
  .footer-curved-section {
    height: 250px;
  }
  
  .footer-bottom {
    height: 250px;
    padding: 1rem;
  }

  .footer-box {
    height: 180px;
  }

  .footer-logo {
    height: 35px;
  }

  .footer-box-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .footer-logo-section {
    grid-column: 1;
    align-items: center;
  }

  .footer-solutions,
  .Footer-industry,
  .footer-company-size,
  .footer-resources {
    grid-column: 1;
    display: block;
  }

  .social-icon {
    width: 32px;
    height: 32px;
    font-size: 0.9rem;
  }

  .footer-copyright {
    font-size: 0.8rem;
  }

  .footer-solutions-title {
    font-size: 1rem;
    margin-bottom: 0.8rem;
  }

  .footer-solution-link {
    font-size: 0.85rem;
  }
}
